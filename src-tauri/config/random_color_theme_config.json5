{"module": {"com.android.settings": true, "com.android.contacts": true, "com.android.mms": true, "com.android.systemui": true, "miui.systemui.plugin": true, "com.miui.notification": true, "com.miui.securitycenter": true, "android": true, "com.miui.home": true, "com.miui.fliphome": true}, "transparent_img": [".*transparent.*", ".*_fg_.*", ".*_foreground_.*", "miuix_appcompat_action_button_bg.png", "miuix_appcompat_action_mode_button_bg_light.png", "miuix_preference_item_foreground_light.png"], "transparent_color": [".*transparent.*"], "delete_img": ["miui_volume_timer_progress_drawable_dragging_transparent.png", "miui_volume_timer_progress_drawable_fg_transparent.png", "volume_row_seekbar_progress.png", "volume_row_seekbar.png", "accessibility_floating_menu_background.png", "miuix_preference_ic_connecting_translation.png", "vector_drawable_progress_indeterminate_horizontal_trimmed.png", "abc_vector_test.png", "vector_drawable_progress_bar_small.png", "action_button_search.png", "action_button_refresh.png", "miuix_popup_window_list_item_fg.png", "expand_button_pill_bg.png", "workspace_seekpoint.png", "workspace_seekpoint_7dp.png", "storage_list_item_dot_bg.png", "miuix_preference_ic_bg_connect.png", "nearby_wifi_bt_refresh__0.png", "nearby_wifi_bt_refresh.png", "bt_refresh__0.png", "bt_refresh_background.png", "bt_refresh.png", "nearby_wifi_bt_refresh__0.png", "nearby_wifi_bt_refresh.png"], "delete_color": [], "add_image": {"com.android.settings": ["progress_indeterminate_horizontal_material_trimmed.png"], "framework-res": ["progress_indeterminate_anim_large_material.png", "progress_indeterminate_anim_medium_material.png", "progress_indeterminate_horizontal_holo.png", "progress_indeterminate_horizontal_material.png", "progress_large_material.png", "progress_medium_material.png"]}}