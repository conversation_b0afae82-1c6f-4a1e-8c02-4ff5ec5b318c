/* 搜索颜色页面样式 */

/* 颜色范围搜索容器样式 - 极简风格 */
.color-range-search-container {
    position: fixed;
    top: 5px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 4px;
    height: 40px;
    z-index: 999999;
    pointer-events: none;
    /* 容器本身不响应点击事件 */
}

/* 颜色范围搜索输入框样式 - 极简风格 */
.color-range-search-input {
    width: 200px;
    padding: 3px 10px;
    border: 1px solid #00000020;
    border-radius: 8px;
    height: 30px;
    font-size: 10px;
    font-family: 'JetBrains Mono', monospace;
    outline: none;
    transition: border-color 0.2s ease;
    background: transparent;
    color: #333;
    pointer-events: auto;
    position: relative;
    z-index: 999999;
    /* 重新启用输入框的点击事件 */
}

.color-range-search-input:focus {
    border-color: #4169e1;
}

.color-range-search-input::placeholder {
    color: #9097b2;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    font-size: 9px;
}

/* 颜色范围搜索按钮样式 - 极简文字按钮 */
.color-range-search-btn {
    padding: 2px 5px;
    background: transparent;
    color: #4169e1;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 10px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    pointer-events: auto;
    position: relative;
    z-index: 999999;
    /* 重新启用按钮的点击事件 */
}

.color-range-search-btn:hover {
    background: rgba(65, 105, 225, 0.08);
    color: #3557c7;
}

.color-range-search-btn:active {
    background: rgba(65, 105, 225, 0.15);
}

/* 清空按钮样式 - 纯文字版本 */
.clear-all-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    color: #8d94b0;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    /* text-decoration: underline; */
}

.clear-all-button:hover {
    color: #ed6a5f;
    transform: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

.clear-all-button:active {
    color: #8d94b0;
    transform: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* 添加按钮样式 - 纯文字版本 */
.add-screenshot-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    color: #8d94b0;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    /* text-decoration: underline; */
    z-index: 9999;
}

.add-screenshot-button:hover {
    color: #3158d0;
    transform: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

.add-screenshot-button:active {
    color: #2142a8;
    transform: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

.add-del-icon {
    width: 20px;
    height: 20px;
    display: block;
    transition: none;
}

.add-screenshot-button:hover .add-del-icon {
    transform: none;
}

.add-screenshot-button:active .add-del-icon {
    transform: none;
}

.clear-all-button:hover .add-del-icon {
    transform: none;
}

.clear-all-button:active .add-del-icon {
    transform: none;
}

/* 按钮动画类 */
.clear-button-entered {
    opacity: 1;
    transform: translateY(0);
}

.clear-button-entering,
.clear-button-exiting {
    opacity: 0;
    transform: translateY(10px);
}

.add-button-moving {
    animation: bounce 0.5s ease;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-10px);
    }
}

/* 截图容器样式 */
.screenshots-container {
    display: flex;
    flex-wrap: nowrap;
    /* 不换行 */
    gap: 14px;
    /* 截图之间的间距 */
    overflow-x: auto;
    /* 水平滚动 */
    overflow-y: visible;
    /* 允许垂直方向溢出显示，以便描边不被裁剪 */
    border-radius: 0px;
    padding: 20px;
    /* 内边距 */
    margin: 0;
    /* 重置外边距 */
    justify-content: flex-start;
    /* 从左侧开始排列 */
    align-items: center;
    /* 中间对齐 */
    width: auto;
    /* 自适应宽度 */
    /* 底部固定定位 */
    position: fixed;
    bottom: 0px;
    /* 距离底部20px */
    left: 0;
    right: 0;
    z-index: 100;
    /* 提高层级，确保在预览图之上 */

    /* 移除背景，使其透明 */
    background-color: transparent;
    /* 不使用模糊效果，保持透明 */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;

    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
    /* 平滑滚动 */
    scroll-behavior: smooth;
    /* 平滑滚动 */
    will-change: transform;
    /* 告诉浏览器这个元素会变化 */
    transform: translateZ(0);
    /* 强制硬件加速 */
}

/* 隐藏滚动条但保持滚动功能 */
.screenshots-container::-webkit-scrollbar {
    height: 0px;
    width: 0px;
    background: transparent;
}

.screenshots-container::-webkit-scrollbar-track {
    background-color: transparent;
}

.screenshots-container::-webkit-scrollbar-thumb {
    background-color: transparent;
}

.screenshots-container::-webkit-scrollbar-thumb:hover {
    background-color: transparent;
}

/* 截图项样式 */
.screenshot-item {
    position: relative;
    bottom: -10px;
    left: 0;
    width: 80px;
    overflow: visible;
    /* 允许子元素溢出显示，以便描边可见 */
    transition: all 0.15s ease;
    flex-shrink: 0;
    /* 防止被压缩 */
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: transparent;
    border: 2px solid transparent;
    border-radius: 6px;
    padding: 2px;
    box-sizing: border-box;
    cursor: pointer;

    /* 优化渲染性能 */
    will-change: opacity, transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 增强悬停效果 */
.screenshot-item:hover:not(.active) {
    transform: translateY(-3px) scale(1.02);
    filter: brightness(1.05);
}

/* 增强点击效果 */
.screenshot-item:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
}

/* 移除截图项的边框高亮，改为只高亮图片 */
.screenshot-item.active {
    border-color: transparent;
    box-shadow: none;
}

/* 截图图片容器 */
.screenshot-img-container {
    width: 100%;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
    /* 允许描边溢出显示 */
    cursor: pointer;
    position: relative;
    /* 为伪元素定位做准备 */
}

/* 截图中的图片样式 */
.screenshot-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    /* 保持原始比例 */
    cursor: pointer;
    display: block;
    border-radius: 4px;
    box-sizing: border-box;
    /* 添加平滑过渡 */
    transition: all 0.2s ease;
}

/* 选中状态下使用伪元素创建描边效果 */
.screenshot-item.active .screenshot-img-container::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #4169e1;
    /* 蓝色描边，不透明 */
    border-radius: 6px;
    box-shadow: 0 0 8px rgba(65, 105, 225, 0.6);
    /* 轻微的蓝色阴影 */
    pointer-events: none;
    /* 确保不影响点击事件 */
    z-index: 2;
    /* 确保在内容上方可见 */
}

/* 移除其他可能影响选中效果的样式 */
.screenshot-item.active .screenshot-img-container {
    background-color: transparent;
    /* 移除背景色 */
    border-color: transparent;
    /* 移除边框 */
}

/* 删除旧的顶部删除按钮样式，替换为新的底部删除按钮 */
.delete-screenshot-btn {
    display: none;
    /* 隐藏旧的删除按钮 */
}

/* 图片底部的序号和删除按钮容器 */
.screenshot-footer {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    font-size: 10px;
    flex-direction: column;
    text-align: center;
}

/* 序号样式 */
.screenshot-number {
    margin-right: 0;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    transition: color 0.2s ease;
}

/* 底部删除按钮样式 */
.screenshot-delete-btn {
    color: #666;
    cursor: pointer;
    border: none;
    background: none;
    padding: 2px 5px;
    font-size: 12px;
    transition: all 0.2s ease;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
}

/* 激活状态下的序号和删除按钮颜色 */
.screenshot-item.active .screenshot-number,
.screenshot-item.active .screenshot-delete-btn {
    color: #4169e1;
    /* 使用与边框相同的蓝色 */
}

/* 保持删除按钮悬停效果 */
.screenshot-delete-btn:hover {
    color: #ff5f57;
    text-decoration: underline;
}

/* 基础字体设置 - 中英文混排字体策略 */
body,
button,
input,
textarea,
select,
div,
p,
span,
a {
    /* 使用字体回退策略：中文优先使用MiSans，英文数字使用JetBrains Mono */
    font-family: 'MiSans', 'JetBrains Mono', 'Helvetica Neue', 'Arial', sans-serif;
}

/* 专门针对英文和数字的选择器 */
.english-text,
.number,
code,
pre,
kbd,
.color-value,
/* 颜色值 */
.hex-value,
/* 十六进制值 */
.rgb-value,
/* RGB值 */
.resource-name

/* 资源名称通常包含英文和数字 */
    {
    font-family: 'JetBrains Mono', monospace !important;
}

/* 为所有带有数字的元素应用字体特性设置 */
body {
    /* 这些设置会应用到所有数字，无论它们在哪个字体中 */
    font-feature-settings: "tnum";
    /* 等宽数字 */
    -webkit-font-feature-settings: "tnum";
    -moz-font-feature-settings: "tnum";
}

/* 截图前设置区域布局样式 */
.add-screenshot-guide {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 96%;
    /* 计算可用高度：总高度 - 标题栏(36px) - 底部内容区域(260px) */
    height: calc(100vh - 36px - 360px);
    box-sizing: border-box;
    position: relative;
    z-index: 10;
}

.screenshot-section-title,
.screenshot-section-warning {
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    font-weight: 500;
    font-size: 15px;
    font-weight: 500;
    color: rgba(65, 105, 225, 0.8);
    margin-bottom: 8px;
    letter-spacing: 0.3px;
}

.screenshot-list-item,
.screenshot-warning-item {
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 5px;
}

/* 颜色值容器样式 */
.color-value-container {
    font-family: 'JetBrains Mono', monospace;
}

/* 颜色名称样式 */
.color-name {
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
}

/* 颜色值样式 */
.color-hex,
.color-rgb,
.color-resource {
    font-family: 'JetBrains Mono', monospace;
    letter-spacing: 0.5px;
}

/* 资源名称样式 */
.resource-id {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 500;
}

/* 颜色搜索结果窗口样式 - 采用设备选择界面风格 */
.color-search-results-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2000;

    /* 与设备选择界面相同的背景效果 */
    background: rgba(248, 248, 248, 0.85);
    backdrop-filter: blur(20px) saturate(120%);

    /* 使用flexbox实现完美居中 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;

    /* 确保内容不会超出屏幕 */
    box-sizing: border-box;
    overflow: hidden;

    /* 优雅的淡入动画 */
    animation: overlayFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.color-search-results-container {
    width: 420px;
    /* 加宽容器 */
    display: flex;
    flex-direction: column;
    max-height: 80vh;
    /* 为缩放效果预留空间 */
    box-sizing: border-box;
}

/* 固定头部 */
.color-search-results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 10px 10px 15px;
    flex-shrink: 0;
    /* 防止头部被压缩 */
    gap: 12px;
}

/* 可滚动内容区域 */
.color-search-results-content {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 12px;
    padding: 0 10px 20px 10px;
    overflow-y: auto;
    flex: 1;
    /* 占据剩余空间 */
}

/* 覆盖层标题 - 采用设备选择界面风格 */
.color-search-results-title {
    margin: 0;
    font-size: 11px;
    font-weight: 500;
    color: #1d1d1f;
    text-align: left;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
    flex: 1;
    /* 占据剩余空间 */
}

/* Target分组样式 - 采用设备选择界面的按钮风格 */
.color-search-target-section {
    width: 100%;
    margin: 0;
    /* 为缩放效果预留空间 */
    padding: 2px;
    box-sizing: border-box;
}

.color-search-target-header {
    box-sizing: border-box;
    width: calc(100% - 4px);
    height: 32px;
    padding: 0 12px;
    border-radius: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.9);
    color: #1d1d1f;
    transition: all 0.15s ease-out;
    cursor: pointer;
    margin: 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

.color-search-target-header:hover {
    background: rgba(245, 245, 247, 0.95);
    border-color: rgba(0, 0, 0, 0.1);
    transform: translateY(-1px) scale(1.02);
}

.color-search-target-header:active {
    background: rgba(230, 230, 230, 0.9);
    transform: translateY(0px) scale(0.98);
    transition-duration: 0.05s;
}

.color-search-target-title {
    margin: 0;
    font-size: 12px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
    flex: 1;
    text-align: left;
    line-height: 19px;
}

.color-search-target-hint {
    font-size: 9px;
    font-weight: 500;
    color: #007aff;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

/* 颜色列表 - 采用设备项目的展开风格 */
.color-search-color-list {
    width: calc(100% - 4px);
    margin: 8px 2px 0 2px;
    display: none;
    /* 默认隐藏，点击展开 */
    /* 为缩放效果预留空间 */
    padding: 2px 0;
    box-sizing: border-box;
}

.color-search-color-list.expanded {
    display: block;
}

.color-search-color-item {
    box-sizing: border-box;
    height: 32px;
    padding: 0 12px;
    border-radius: 4px;
    border: none;
    background: rgba(255, 255, 255, 0.85);
    transition: all 0.1s ease-out;
    cursor: pointer;
    margin: 2px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
    width: calc(100% - 4px);
}

.color-search-color-item:hover {
    background: rgba(245, 245, 247, 0.9);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.color-search-color-item:active {
    transform: scale(0.98);
    transition-duration: 0.05s;
}

.color-search-color-preview {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.1);
}

.color-search-color-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.color-search-color-value {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
    font-size: 9px;
    font-weight: 500;
    color: #000;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;
}

.color-search-color-name {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
    font-size: 9px;
    font-weight: 500;
    color: #007aff;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

/* 执行按钮容器 - 垂直布局，分类显示 */
.color-search-action-container {
    width: calc(100% - 6px);
    margin: 6px 3px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 按钮分类标签 */
.color-search-action-label {
    font-size: 10px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.7);
    margin-bottom: 2px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

/* 按钮行容器 */
.color-search-buttons-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
}

/* 执行按钮样式 - 等宽设计，使用设备选择背景色 */
.color-search-action-btn {
    box-sizing: border-box;
    width: 62px;
    /* 固定等宽 */
    height: 24px;
    padding: 0;
    border-radius: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.08);
    transition: all 0.15s ease-out;
    cursor: pointer;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
    white-space: nowrap;
    flex-shrink: 0;
    background: rgba(242, 242, 247, 1);
    /* 使用设备选择的背景色 */
    color: rgba(0, 0, 0, 0.8);
}

/* 按钮悬停和激活效果 */
.color-search-action-btn:hover {
    background: rgba(235, 235, 245, 1);
    transform: translateY(-1px) scale(1.02);
    border-color: rgba(0, 0, 0, 0.12);
}

.color-search-action-btn:active {
    transform: translateY(0px) scale(0.98);
    transition-duration: 0.05s;
    background: rgba(225, 225, 235, 1);
}

/* 颜色值按钮 - 绿色系 */
.color-search-action-color {
    background: rgba(52, 199, 89, 0.85);
    color: white;
    border-color: rgba(52, 199, 89, 0.3);
}

.color-search-action-color:hover {
    background: rgba(52, 199, 89, 1);
    border-color: rgba(52, 199, 89, 0.4);
}

.color-search-action-color:active {
    background: rgba(40, 180, 75, 1);
}

/* 图片按钮 - 橙色系 */
.color-search-action-image {
    background: rgba(255, 149, 0, 0.85);
    color: white;
    border-color: rgba(255, 149, 0, 0.3);
}

.color-search-action-image:hover {
    background: rgba(255, 149, 0, 1);
    border-color: rgba(255, 149, 0, 0.4);
}

.color-search-action-image:active {
    background: rgba(230, 130, 0, 1);
}

/* 完整复制按钮 - 更突出的显示 */
.color-search-action-btn.full-copy {
    font-weight: 600;
    border-width: 1.5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 被点击过的按钮样式 - 黑色背景 */
.color-search-action-btn.clicked {
    background: #000000 !important;
    color: white !important;
    border-color: #000000 !important;
}

.color-search-action-btn.clicked:hover {
    background: #333333 !important;
    color: white !important;
    border-color: #333333 !important;
}

.color-search-action-btn.clicked:active {
    background: #000000 !important;
    color: white !important;
}

.color-search-action-color.full-copy {
    background: rgba(52, 199, 89, 1);
    border-color: rgba(52, 199, 89, 0.5);
}

.color-search-action-color.full-copy:hover {
    background: rgba(40, 180, 75, 1);
    transform: translateY(-1px) scale(1.05);
}

.color-search-action-image.full-copy {
    background: rgba(255, 149, 0, 1);
    border-color: rgba(255, 149, 0, 0.5);
}

.color-search-action-image.full-copy:hover {
    background: rgba(230, 130, 0, 1);
    transform: translateY(-1px) scale(1.05);
}



/* 关闭按钮 - 简约红色文字风格 */
.color-search-results-close {
    background: transparent;
    border: none;
    color: rgba(255, 59, 48, 0.8);
    font-weight: 500;

    box-sizing: border-box;
    /* width: 60px; */
    /* 固定宽度，适合头部布局 */
    height: 28px;
    /* 稍微减小高度 */
    padding: 0 8px;
    border-radius: 6px;
    transition: all 0.15s ease-out;
    cursor: pointer;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    /* 稍微减小字体 */
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
    flex-shrink: 0;
    /* 防止被压缩 */
}

.color-search-results-close:hover {
    /* background: rgba(255, 59, 48, 0.1); */
    color: rgba(255, 59, 48, 1);
    transform: scale(1.05);
}

.color-search-results-close:active {
    /* background: rgba(255, 59, 48, 0.2); */
    color: rgba(255, 59, 48, 1);
    transform: scale(0.95);
    transition-duration: 0.05s;
}

/* 确认对话框样式 - 采用设备选择界面风格 */
.color-search-confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;

    /* 与设备选择界面相同的背景效果 */
    background: rgba(248, 248, 248, 0.85);
    backdrop-filter: blur(20px) saturate(120%);

    /* 使用flexbox实现完美居中 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;

    /* 确保内容不会超出屏幕 */
    box-sizing: border-box;
    overflow: hidden;

    /* 优雅的淡入动画 */
    animation: overlayFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.color-search-confirm-container {
    width: 280px;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 16px;
    padding: 20px 10px;
    box-sizing: border-box;
}

/* 确认消息 - 采用覆盖层标题风格 */
.color-search-confirm-message {
    margin: 0 5% 10px 5%;
    font-size: 11px;
    font-weight: 500;
    color: #1d1d1f;
    text-align: center;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
    white-space: pre-line;
    line-height: 1.4;
}

/* 确认按钮 - 采用设备选择界面的按钮风格 */
.color-search-confirm-btn {
    box-sizing: border-box;
    width: calc(100% - 4px);
    height: 32px;
    padding: 0 12px;
    border-radius: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.9);
    color: #1d1d1f;
    transition: all 0.15s ease-out;
    cursor: pointer;
    margin: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

.color-search-confirm-btn:hover {
    background: rgba(245, 245, 247, 0.95);
    border-color: rgba(0, 0, 0, 0.1);
    transform: translateY(-1px) scale(1.02);
}

.color-search-confirm-btn:active {
    background: rgba(230, 230, 230, 0.9);
    transform: translateY(0px) scale(0.98);
    transition-duration: 0.05s;
}

.color-search-confirm-btn.confirm {
    background: rgba(0, 122, 255, 0.95);
    border-color: rgba(0, 122, 255, 0.3);
    color: white;
    box-shadow:
        0 1px 3px rgba(0, 122, 255, 0.2),
        0 2px 6px rgba(0, 122, 255, 0.1),
        inset 0 0.5px 0 rgba(255, 255, 255, 0.3);
}

.color-search-confirm-btn.confirm:hover {
    background: rgba(0, 122, 255, 1);
    border-color: rgba(0, 122, 255, 0.4);
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 2px 6px rgba(0, 122, 255, 0.3),
        0 4px 12px rgba(0, 122, 255, 0.15),
        inset 0 0.5px 0 rgba(255, 255, 255, 0.3);
}

.color-search-confirm-btn.confirm:active {
    background: rgba(0, 122, 255, 1);
    transform: translateY(0px) scale(0.98);
    transition-duration: 0.05s;
}


/* 顶部按钮容器 */
.top-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 10px;
}

/* 统一的顶部按钮样式 */
.top-buttons-container .module-item-base {
    flex: 1;
    height: 32px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    background: rgba(255, 255, 255, 0.9) !important;
    color: #333 !important;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px !important;
    margin-bottom: 0 !important;
}

/* 统一的顶部按钮悬停效果 */
.top-buttons-container .module-item-base:hover {
    background: rgba(245, 245, 247, 0.95) !important;
    border-color: rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 统一的顶部按钮激活效果 */
.top-buttons-container .module-item-base:active {
    background: rgba(230, 230, 230, 0.9) !important;
    transform: translateY(0px) scale(0.98);
    transition-duration: 0.05s;
}

/* 全选按钮选中状态 */
.top-buttons-container .select-all.selected {
    background: rgba(0, 122, 255, 0.95) !important;
    border-color: rgba(0, 122, 255, 0.3) !important;
    /* 保持文字颜色和字重与其他按钮一致，不使用白色 */
}



/* 动画效果 - 与设备选择界面一致 */
@keyframes overlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px) saturate(100%);
    }

    to {
        opacity: 1;
        backdrop-filter: blur(20px) saturate(120%);
    }
}

/* 颜色搜索容器样式 */
.color-search-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: calc(100vh - 40px);
    /* 填充除了顶部40px的整个高度 */
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
    background-color: #0000;
}

/* 搜索内容区域样式 */
.search-content-area {
    width: 100%;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    /* 如果内容超出可滚动 */
}

/* 中央预览图容器样式 */
.central-preview-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(100% - 40px);
    max-width: 100%;
    margin: 10px auto;
    overflow: hidden;
    background-color: #00000009;
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 260px;
    border-radius: 10px;
    z-index: 50;
    padding: 10px;
    border: 2px solid #00000005;
    box-sizing: border-box;
}

/* 中央预览图样式 */
.central-preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    /* 保持原始比例，自动缩放 */
    display: block;
    border: 2px solid #0001;
    /* 半透明边框 */
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

/* 暂无截图提示样式 */
.no-screenshots {
    width: 100%;
    height: 100%;
    text-align: center;
    justify-content: center;
    align-items: center;
    padding: 100px;
    border-radius: 20px;
    background-color: #fafbff;
    color: #9097b2;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    font-size: 14px;
}

/* 按钮容器 */
.main-buttons-container {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    position: fixed;
    right: 0px;
    bottom: 243px;
    z-index: 101;
    gap: 10px;
    padding: 8px 12px;
}


/* 提示 */
.guide-container {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    position: fixed;
    left: 0px;
    bottom: 247px;
    /* 距离底部200px */
    z-index: 101;
    gap: 10px;
    padding: 8px 16px;
}

.guide {
    font-family: 'JetBrains Mono',
        'Helvetica Neue',
        'Arial',
        sans-serif;
    font-size: 12px;
    color: #8d94b0;
    margin-top: 5px;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {

    /* 顶部按钮容器深色模式 */
    .top-buttons-container .module-item-base {
        border: 1px solid rgba(255, 255, 255, 0.15) !important;
        background: rgba(50, 50, 50, 0.8) !important;
        color: #fff !important;
    }

    .top-buttons-container .module-item-base:hover {
        background: rgba(60, 60, 60, 0.9) !important;
        border-color: rgba(255, 255, 255, 0.25) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .top-buttons-container .module-item-base:active {
        background: rgba(40, 40, 40, 0.9) !important;
    }

    /* 全选按钮选中状态深色模式 */
    .top-buttons-container .select-all.selected {
        background: rgba(0, 122, 255, 0.9) !important;
        border-color: rgba(0, 122, 255, 0.4) !important;
        /* 保持文字颜色和字重与其他按钮一致，不使用白色 */
    }

    /* 颜色范围搜索输入框深色模式 */
    .color-range-search-input {
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(30, 30, 30, 0.8);
        color: #fff;
    }

    .color-range-search-input:focus {
        border-color: #5b9bff;
        background: rgba(30, 30, 30, 0.9);
    }

    .color-range-search-input::placeholder {
        color: #888;
    }

    /* 颜色范围搜索按钮深色模式 */
    .color-range-search-btn {
        color: #5b9bff;
        background: transparent;
    }

    .color-range-search-btn:hover {
        background: rgba(91, 155, 255, 0.15);
        color: #7bb3ff;
    }

    .color-range-search-btn:active {
        background: rgba(91, 155, 255, 0.25);
    }

    /* 清空按钮深色模式 */
    .clear-all-button {
        color: #ff6b6b;
        background: transparent;
    }

    .clear-all-button:hover {
        background: rgba(255, 107, 107, 0.15);
        color: #ff8a8a;
    }

    /* 序号样式 */
    .screenshot-number {
        margin-right: 0;
        margin-bottom: 5px;
        font-weight: bold;
        color: #fff;
        font-family: 'JetBrains Mono', monospace;
        font-size: 14px;
        transition: color 0.2s ease;
    }

    /* 底部删除按钮样式 */
    .screenshot-delete-btn {
        color: #666;
        cursor: pointer;
        border: none;
        background: none;
        padding: 2px 5px;
        font-size: 12px;
        transition: all 0.2s ease;
        font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    }

    /* 颜色值文本深色模式 */
    .color-search-color-value {
        color: #fff;
    }

    /* 被点击过的按钮深色模式 */
    .color-search-action-btn.clicked {
        background: #333333 !important;
        color: white !important;
        border-color: #555555 !important;
    }

    .color-search-action-btn.clicked:hover {
        background: #444444 !important;
        color: white !important;
    }

    /* 搜索内容区域深色模式 */
    .search-content-area {
        background-color: rgba(255, 255, 255, 0.05);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    /* 中央预览图深色模式 */
    .central-preview-img {
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    }

    /* 搜索结果容器深色模式 */
    .search-results-container {
        background-color: rgba(0, 0, 0, 0.1);
    }

    /* 颜色搜索结果窗口深色模式 */
    .color-search-results-overlay {
        background: rgba(0, 0, 0, 0.7);
    }


    .color-search-results-title {
        color: #fff;
    }

    /* Target分组深色模式 */
    .color-search-target-header {
        background: rgba(50, 50, 50, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .color-search-target-header:hover {
        background: rgba(60, 60, 60, 0.9);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .color-search-target-header:active {
        background: rgba(40, 40, 40, 0.9);
    }

    .color-search-target-title {
        color: #fff;
    }

    .color-search-target-hint {
        color: #aaa;
    }

    /* 颜色项深色模式 */
    .color-search-color-item {
        background: rgba(40, 40, 40, 0.6);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .color-search-color-item:hover {
        background: rgba(50, 50, 50, 0.8);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .color-search-color-name {
        color: #ccc;
    }

    /* 执行按钮深色模式 */
    .color-search-action-btn {
        background: rgba(60, 60, 60, 0.8);
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;
    }

    .color-search-action-btn:hover {
        background: rgba(70, 70, 70, 0.9);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .color-search-action-btn:active {
        background: rgba(50, 50, 50, 0.9);
    }

    /* 颜色值按钮深色模式 */
    .color-search-action-color {
        background: rgba(52, 199, 89, 0.9);
    }

    .color-search-action-color:hover {
        background: rgba(52, 199, 89, 1);
    }

    /* 图片按钮深色模式 */
    .color-search-action-image {
        background: rgba(255, 149, 0, 0.9);
    }

    .color-search-action-image:hover {
        background: rgba(255, 149, 0, 1);
    }

    /* 中断按钮深色模式 */
    .color-search-action-stop {
        background: rgba(255, 69, 58, 0.9);
    }

    .color-search-action-stop:hover {
        background: rgba(255, 59, 48, 1);
    }

    /* 关闭按钮深色模式 */
    .color-search-results-close {
        color: #ff6b6b;
    }

    .color-search-results-close:hover {
        color: #ff8a8a;
    }

    /* 确认对话框深色模式 */
    .color-search-confirm-dialog {
        background: rgba(0, 0, 0, 0.8);
    }

    .color-search-confirm-container {
        background: rgba(30, 30, 30, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .color-search-confirm-message {
        color: #fff;
    }

    .color-search-confirm-btn {
        background: rgba(50, 50, 50, 0.8);
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;
    }

    .color-search-confirm-btn:hover {
        background: rgba(60, 60, 60, 0.9);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .color-search-confirm-btn.confirm {
        background: rgba(0, 122, 255, 0.9);
        border-color: rgba(0, 122, 255, 0.4);
    }

    .color-search-confirm-btn.confirm:hover {
        background: rgba(0, 122, 255, 1);
        border-color: rgba(0, 122, 255, 0.5);
    }



    .screenshot-item:hover:not(.active) {
        background: rgba(50, 50, 50, 0.8);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .screenshot-item.active .screenshot-number,
    .screenshot-item.active .screenshot-delete-btn {
        color: #5b9bff;
    }

    .screenshot-delete-btn:hover {
        color: #ff8a8a;
    }
}



/* 应用主题按钮组样式 */
.apply-theme-button-group {
    display: flex;
    align-items: center;
    position: relative;
}

.apply-theme-main-btn {
    border-radius: 4px 0 0 4px !important;
}

.apply-theme-dropdown-btn {
    border-radius: 0 4px 4px 0 !important;
    min-width: auto !important;
    padding: 8px 2px !important;
    margin-left: -5px !important;
}

.apply-theme-dropdown-btn.active {
    color: #3158d0 !important;
}

/* 添加更好的点击反馈效果 */
.apply-theme-main-btn,
.apply-theme-dropdown-btn {
    transition: all 0.15s ease !important;
}

.apply-theme-main-btn:active,
.apply-theme-dropdown-btn:active {
    transform: scale(0.95) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* 下拉菜单样式 */
.apply-theme-dropdown-menu {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateZ(0);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(141, 148, 176, 0.3);
    border-radius: 14px;
    padding: 10px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    min-width: 180px;
    max-height: 450px;
    height: auto;
    overflow-y: auto;

    /* 优化性能 */
    -webkit-overflow-scrolling: touch;
    will-change: auto;
    contain: layout style paint;
}

.apply-theme-dropdown-item {
    box-sizing: border-box;
    height: 32px;
    padding: 0px 12px;
    margin-bottom: 10px !important;
    border-radius: 8px;
    border: none;
    outline: 1px solid #0002;
    transition: all 0.1s ease-out;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 13px;
    color: #333;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    min-width: 0;
}

.apply-theme-dropdown-item:hover:not(.disabled) {
    background: rgba(245, 245, 247, 0.9);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
    transform: none;
}

.apply-theme-dropdown-item:active:not(.disabled) {
    transform: scale(0.98);
    transition-duration: 0.05s;
    background: rgba(235, 235, 237, 0.9);
}

.apply-theme-dropdown-item.selected {
    color: #007aff;
    background: rgba(0, 122, 255, 0.08);
    font-weight: 600;
}

.apply-theme-dropdown-item.disabled {
    background: rgba(248, 248, 248, 0.6);
    cursor: not-allowed;
    opacity: 0.4;
    color: #8e8e93;
}

.apply-theme-dropdown-item.disabled:hover {
    transform: none;
    background: rgba(248, 248, 248, 0.6);
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
}

.apply-theme-dropdown-item.select-all {
    height: 32px;
    /* font-weight: 600; */
    border-bottom: 1px solid rgba(141, 148, 176, 0.1);
}

.apply-theme-dropdown-separator {
    height: 1px;
    background-color: rgba(141, 148, 176, 0.2);
    margin: 4px 0;
}



/* 旧版生成按钮样式（保持兼容性） */
.apply-theme-apply-btn:not(.top-buttons-container .apply-theme-apply-btn) {
    margin: 8px;
    padding: 6px 12px;
    background-color: #3158d0;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    transition: background-color 0.2s ease;
}

.apply-theme-apply-btn:not(.top-buttons-container .apply-theme-apply-btn):hover {
    background-color: #2142a8;
}

/* 添加模块功能样式 */
.add-module-section {
    padding: 8px 12px;
    display: flex;
    gap: 8px;
    align-items: center;
}

.add-module-input {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid rgba(141, 148, 176, 0.3);
    border-radius: 4px;
    font-size: 12px;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.add-module-input:focus {
    outline: none;
    border-color: #3158d0;
    box-shadow: 0 0 0 2px rgba(49, 88, 208, 0.1);
}

.add-module-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-family: 'MiSans', 'Helvetica Neue', 'Arial', sans-serif;
    transition: background-color 0.2s ease;
}

.add-module-btn:hover {
    background: #218838;
}

.add-module-btn:active {
    background: #1e7e34;
    transform: scale(0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.apply-theme-apply-btn:not(.top-buttons-container .apply-theme-apply-btn):active {
    background-color: #1a3586;
    transform: scale(0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 深色模式下的应用主题按钮样式 */
@media (prefers-color-scheme: dark) {
    .apply-theme-dropdown-menu {
        background: rgba(45, 45, 45, 0.98);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .apply-theme-dropdown-item {
        background: rgba(45, 45, 45, 0.85);
        color: #fff;
    }

    .apply-theme-dropdown-item:hover:not(.disabled) {
        background: rgba(55, 55, 55, 0.9);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        transform: none;
    }

    .apply-theme-dropdown-item:active:not(.disabled) {
        background: rgba(35, 35, 35, 0.9);
        transform: scale(0.98);
        transition-duration: 0.05s;
    }

    .apply-theme-dropdown-item.selected {
        color: #5b9bff;
        background: rgba(91, 155, 255, 0.12);
        font-weight: 600;
    }

    .apply-theme-dropdown-item.disabled {
        background: rgba(38, 38, 38, 0.6);
        color: #666;
        opacity: 0.4;
    }

    .apply-theme-dropdown-item.disabled:hover {
        transform: none;
        background: rgba(38, 38, 38, 0.6);
        box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
    }

    .apply-theme-dropdown-separator {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .add-module-input {
        background: rgba(45, 45, 45, 0.9);
        color: #fff;
        border-color: rgba(255, 255, 255, 0.2);
    }

    .add-module-input:focus {
        border-color: #5b9bff;
        box-shadow: 0 0 0 2px rgba(91, 155, 255, 0.1);
    }

    /* 按钮分类标签 */
.color-search-action-label {
    color: rgba(255, 255, 255, 0.7);
}

.no-screenshots {
    background-color: #333;
    color: #9097b2;
}

/* 固定控制区域容器 */
.generate-module-fixed-controls {
    position: sticky;
    top: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 20px;
    z-index: 10;
}

/* 顶部按钮容器 */
.top-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 0;
}

/* 下拉按钮样式 */
.apply-theme-dropdown-btn {
    padding: 8px 2px;
    margin-left: -5px;
}

.apply-theme-dropdown-btn.version-btn {
    padding: 8px 6px;
    margin-left: 2px;
}

/* 可滚动模块列表 */
.scrollable-module-list {
    max-height: 50vh;
    overflow-y: auto;
    padding: 8px 16px 30px 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 模块项基础样式 */
.module-item-base {
    /* padding: 8px 12px; */
    /* margin-bottom: 4px; */
    /* border-radius: 6px; */
    border: 1px solid rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.8);
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    /* font-size: 12px; */
    /* font-weight: 500; */
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

/* 对话框遮罩 */
.add-module-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

/* 对话框容器 */
.add-module-dialog {
    background: white;
    border-radius: 12px;
    padding: 24px;
    width: 320px;
    max-width: 90vw;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    animation: dialogFadeIn 0.3s ease-out;
}

/* 对话框标题 */
.add-module-dialog h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

/* 对话框输入框 */
.add-module-dialog input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    margin-bottom: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
    background: #fff;
    color: #333;
}

.add-module-dialog input:focus {
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 按钮容器 */
.dialog-button-container {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 取消按钮 */
.cancel-button {
    padding: 8px 16px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background: white;
    color: #333;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.cancel-button:hover {
    background: #f8f9fa;
    border-color: rgba(0, 0, 0, 0.25);
}

/* 确认按钮 */
.confirm-button {
    padding: 8px 16px;
    border: none;
    background: #007AFF;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.25);
}

.confirm-button:hover {
    background: #0056CC;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
}

/* 深色模式下的UI组件样式 */
@media (prefers-color-scheme: dark) {
    /* 固定控制区域深色模式 */
    .generate-module-fixed-controls {
        background: rgba(30, 30, 30, 0.95);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* 可滚动模块列表深色模式 */
    .scrollable-module-list {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* 模块项深色模式 */
    .module-item-base {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(50, 50, 50, 0.8);
        color: #fff;
    }

    /* 对话框深色模式 */
    .add-module-dialog {
        background: rgba(30, 30, 30, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .add-module-dialog h3 {
        color: #fff;
    }

    .add-module-dialog input {
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(50, 50, 50, 0.8);
        color: #fff;
    }

    .cancel-button {
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(50, 50, 50, 0.8);
        color: #fff;
    }

    .cancel-button:hover {
        background: rgba(60, 60, 60, 0.9);
        border-color: rgba(255, 255, 255, 0.3);
    }
}
}

html,
body {
    width: 100vw;
    height: 100vh;
    min-width: 0;
    min-height: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

/* 主内容区自适应填充 */
#main-content,
.main-content {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

/* 兼容原有布局，移除写死宽高的样式 */