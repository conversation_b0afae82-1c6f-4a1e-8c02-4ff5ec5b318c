@import './script/random_color_theme/search_color.css';
@import './script/special_overlay_button.css';

:root {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    --font-size: 13px;
    cursor: default;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    color: #0f0f0f;
    font-weight: 500;
    -webkit-font-weight: 500;
    -moz-font-weight: 500;
    -ms-font-weight: 500;
    line-height: 24px;
    font-family: "MiSans", sans-serif, Inter, Avenir, Helvetica, Arial;
    font-synthesis: none;
    user-select: none;
    text-rendering: optimizeLegibility;
    /* 颜色变量定义 */
    --primary-color: #4169e1;
    --primary-color-light: #4169e1;
    --primary-color-dark: #4169e1;
    --background-color: #f5f5f5;
    --text-color: #333;
    --header-color: #fff;
    --border-color: #eaeaea;
    --hover-color: #f8f9fa;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --gradient-from: rgba(255, 255, 255, 0);
    --gradient-to: rgba(255, 255, 255, 1);
    --secondary-color: #6c757d;
}

html {
    position: relative;
    overflow: hidden;
}

html,
body {
    clip: rect(0, auto, auto, 0);
    content-visibility: auto;
    transform: scale(1);
    backface-visibility: hidden;
    contain: strict;
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden !important;
    background-color: #ffffffff;
    border-radius: 8px;
}

/* 移除所有元素在获得焦点时的轮廓 */
*:focus {
    outline: none !important;
}

.container {
    -ms-overflow-style: none;
    display: flexbox;
    flex-direction: column;
    justify-content: center;
    transform: scale(1);
    margin: 0;
    padding-top: 40px;
    min-width: 100vw;
    max-width: 100vw;
    height: 100vh;
    overflow: hidden !important;
    scrollbar-width: none;
    text-align: center;
}

.row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 10px 14px;
    width: calc(100vw - 28px);
}

.apply-widget-button-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 每行3个按钮 */
    grid-template-rows: repeat(2, 1fr);
    /* 显示2行 */
    justify-content: center;
    align-items: center;
    /* 上下间距固定20px,左右间距根据容器宽度自动计算 */
    gap: 20px calc((100% - (60px * 3)) / 2);
    /* 60px是按钮最大宽度 */
    box-sizing: border-box;
    margin: 0 10px;
    max-height: calc(34px * 2 + 20px);
    /* 2行按钮高度(34px)加上间距 */
}

.widget-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    appearance: none;
    transition: all 300ms cubic-bezier(0.23, 1, 0.32, 1);
    will-change: transform;
    box-sizing: border-box;
    margin: 0;
    outline: 0;
    border: 1px solid #0003;
    border-radius: 20px;
    background-color: #0000;
    width: calc((100% - 28px) / 3);
    min-width: 60px;
    max-width: 60px;
    height: 30px;
    min-height: 34px;
    max-height: 34px;
    touch-action: manipulation;
    color: #000;
    font-weight: 600;
    font-size: var(--font-size);
    line-height: normal;
    user-select: none;
    text-decoration: none;
}

.apply-theme-button-container,
.tools-button-container {
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 10px;
    padding: 20px 11.5px 25px 11.5px;
    width: calc(100% - 23px);
    overflow-x: auto;
    overflow-y: hidden;
    scroll-padding: 10px;
    white-space: nowrap;
}

.apply-theme-button-container::-webkit-scrollbar,
.tools-button-container::-webkit-scrollbar {
    display: none;
}

.mtz-name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    outline: none;
    box-shadow: #0001 0 0px 10px;
    border: 1px solid #0002;
    border-radius: 10px;
    background-color: #fff;
    width: 100%;
    height: 40px;
    overflow: hidden;
    font-weight: 400;
    font-size: var(--font-size);
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mtz-name-text {
    display: flex;
    align-items: center;
    margin: 10px;
    width: calc(100% - 18px);
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mtz-name-icon {
    stroke: #4169e1;
    fill: #4169e1;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    transition: all 0.3s ease;
    margin-right: 5px;
    border-radius: 50%;
    background-color: #0000;
    padding: 5px;
    width: 14px;
    height: 14px;

    svg {
        stroke: #0000;
        opacity: 1;
    }
}

.mtz-name-icon:hover {
    stroke: #fff;
    fill: #fff;
    box-shadow: #4169e1 0 10px 20px;
    background-color: #4169e1;
}

.button_max:not(:hover):not(:focus),
.button_max {
    -webkit-appearance: none;
    -moz-appearance: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    display: inline-block;
    position: relative;
    appearance: none;
    transition: all 300ms cubic-bezier(0.23, 1, 0.32, 1);
    /* overflow: hidden; */
    transition: all 0.3s ease;
    will-change: transform;
    box-sizing: border-box;
    outline: 0;
    border: none;
    border-radius: 8px;
    background-color: #4169e1;
    padding: 0px 14px;
    width: 100%;
    height: 40px;
    min-height: 40px;
    max-height: 36px;
    overflow: hidden;
    touch-action: manipulation;
    color: #fff;
    font-weight: 600;
    font-size: var(--font-size);
    line-height: normal;
    user-select: none;
    text-align: center;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.button {
    -webkit-appearance: none;
    -moz-appearance: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    display: inline-block;
    appearance: none;
    transition: all 300ms cubic-bezier(0.23, 1, 0.32, 1);
    will-change: transform;
    box-sizing: border-box;
    margin: 0;
    outline: 0;
    border: 1px solid #0003;
    border-radius: 20px;
    background-color: #0000;
    padding: 0px 16px;
    width: auto;
    height: 30px;
    min-height: 34px;
    max-height: 34px;
    touch-action: manipulation;
    color: #000;
    font-weight: 600;
    font-size: var(--font-size);
    line-height: normal;
    user-select: none;
    text-align: center;
    text-decoration: none;
}

.svgIcon {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    opacity: 0.7;
    transition: all 0.3s ease-out;
    width: 20px;
    height: 20px;
    user-select: none;
}

p {
    position: absolute;
    color: #4169e1;
    font-weight: 700;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.icon_button {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease-out;
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0);
    padding: 10px;
    width: 40px;
    min-width: 40px;
    max-width: 40px;
    height: 40px;
    min-height: 40px;
    max-height: 40px;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: translateZ(0);
}

.icon_button:hover .bg {
    position: absolute;
    top: 0;
    left: 0;
    clip-path: circle(50%);
    filter: blur(5px);
    animation: gradientMove 6s linear infinite;
    border-radius: 50%;
    background: linear-gradient(45deg, #fb0094, #0000ff, #0088ff, #6095f5, #ff0000, #fb0094, #0000ff, #9900ff, #a95a00, #ff0000);
    background-size: 400% 400%;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.icon_button:hover .svgIcon {
    opacity: 1;
    filter: invert(1);
}

.icon_button p {
    transform: translateY(0px) scaleX(0);
    opacity: 0;
    transition: all 0.3s ease;
    font-size: 10px;
}

.icon_button:hover p {
    transform: translateY(-30px) scaleX(1);
    opacity: 1;
    transition: all 0.3s ease;
}

@keyframes gradientMove {
    from {
        background-position: 400% 50%;
    }

    to {
        background-position: 0% 50%;
    }
}

.icon_button:disabled,
.mtz-name:disabled,
.widget-button:disabled,
.button_max:disabled,
.button:disabled {
    pointer-events: none;
}

.icon_button:hover,
.widget-button:hover,
.button:hover {
    transform: translateY(-3px);
    box-shadow: rgba(0, 0, 0, 0.4) 0 8px 20px;
    border: 1px solid #0003;
}

.mtz-name:hover {
    transform: translateY(-3px);
    box-shadow: #4169e133 0 3px 20px;
}

.button_max:hover {
    transform: translateY(-3px);
    box-shadow: rgba(0, 0, 0, 0.4) 0 8px 20px;
}

.icon_button:active,
.widget-button:active,
.button:active {
    transform: translateY(0px);
    filter: hue-rotate(45deg);
    border: 1px solid #0003;
    box-shadow: none;
    transition: transform 0s, filter 0s;
}

.mtz-name:active,
.button_max:active {
    transform: translateY(0px);
    transition: transform 0s, filter 0s;
    box-shadow: none;
}

.custom-title-bar {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0;
    margin: 0;
    /* justify-content: space-between; */
    /* align-items: center; */
    /* transform: scale(1); */
    z-index: 999999;
    background-color: transparent;
    width: 100% !important;
    height: 36px;
    user-select: none;
}

.title_icon {
    width: 14px;
    /* height: 16px; */
}

#title-bar-controls {
    display: inline-flex;
    position: absolute;
    /* background-color: #0001; */
    top: 0px;
    left: 0px;
    height: 36px;
    width: 80px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-left: 0px;
    border: none;
}

#title-bar-controls button {
    border: none;
    background: transparent;
    padding: 0;
    width: 12px;
    height: 12px;
    white-space: nowrap;
}

#title-bar-controls button img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
}

button[data-tooltip]::after {
    display: none;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    z-index: 10;
    transition: opacity 0.3s;
    pointer-events: none;
    content: attr(data-tooltip);
    color: #000000;
    white-space: nowrap;
}

button.show-tooltip::after {
    opacity: 1;
}

.message-container {
    display: flex;
    position: absolute;
    top: 30px;
    left: 50%;
    justify-content: center;
    align-items: center;
    transform: translateX(-50%) translateY(-50px);
    opacity: 0;
    z-index: 999999999999999;
    backdrop-filter: blur(10px);
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out, width 0.3s ease-in-out;
    box-shadow: #0002 0 8px 20px;
    border: 1px solid #0002;
    border-radius: 10px;
    background-color: #0000;
    padding: 5px 10px;
    width: fit-content;
    max-width: 80%;
    height: auto;
    max-height: 40px;
    color: #000;
    font-weight: 400;
    font-size: var(--font-size);
    white-space: nowrap;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    pointer-events: none;
}

.message-container>div:nth-child(2) {
    transition: width 0.3s ease-in-out;
}

.message-container.show {
    transform: translateX(-50%) translateY(0px);
    opacity: 1;
}

.button_hidden {
    transition: all 0.5s;
    pointer-events: none;
}

.button_show {
    transition: all 0.5s;
    border: none;
    border-radius: 20px;
    background-color: #4169e1;
    color: #fff;
}

.button_show:hover {
    background-color: #4169e100;
    border: none;
}

.button_show::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    visibility: hidden;
    opacity: 0;
    z-index: -1;
    clip-path: inset(0 round 20px);
    transition: opacity 0.5s, visibility 0.5s 0.3s;
    background: linear-gradient(-45deg, #fb0094, #0000ff, #0088ff, #6095f5, #ff0000, #fb0094, #0000ff, #9900ff, #a95a00, #ff0000);
    background-size: 400% 400%;
    content: '';
}

.button_show:active::before,
.button_show:hover::before {
    visibility: visible;
    opacity: 1;
    filter: blur(5px);
    animation: gradientMove 6s linear infinite;
    transition: opacity 0.3s, visibility 0s;
}

span {
    opacity: 0.7;
    margin-top: 10px;
    padding: 0;
    height: 20px;
    font-size: 12px;
}

.device-container {
    /* 隐藏滚动条 */
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
    display: flex;
    position: absolute;
    top: calc(100vh - 24px);
    left: 50%;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    transform: translateX(-50%);
    opacity: 1;
    z-index: 9999;
    transition: all 0.5s;
    margin: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0);
    border: 1px solid #0000;
    border-radius: 14px;
    background-color: rgb(240, 240, 240);
    padding: 0;
    width: calc(100vw - 20px);
    height: calc(100vh - 50px);
    overflow: hidden;
    overflow-y: scroll;
}

/* 专门针对Webkit内核浏览器(Chrome/Safari等)隐藏滚动条 */
.device-container::-webkit-scrollbar {
    display: none;
}

.device-container-on {
    top: 70px;
    box-sizing: border-box;
    border: 1px solid #0001;
    border-radius: 10px 10px 0 0;
    background-color: #eee;
    width: 200px;
    min-width: 200px;
    max-width: 200px;
    height: calc(100vh - 70px);
}

.device-item-n {
    transition: all 0.3s;
    box-sizing: border-box;
    margin: 10px 10px;
    border: 1px solid #4169e1;
    border-radius: 8px;
    background-color: #4169e1;
    padding: 4px 8px;
    width: 180px;
    min-width: 180px;
    max-width: 180px;
    overflow: hidden;
    color: #fff;
    font-size: 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.device-item-s {
    transition: all 0.3s;
    box-sizing: border-box;
    margin: 10px 10px;
    border: 1px solid #0003;
    border-radius: 8px;
    background-color: #4169e100;
    padding: 4px 8px;
    width: 180px;
    min-width: 180px;
    max-width: 180px;
    overflow: hidden;
    color: #4169e1;
    font-size: 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.device-horizontal-strip {
    opacity: 1;
    transition: all 0.3s;
    margin-top: 9px;
    margin-bottom: 0px;
    border-radius: 10px;
    background-color: #0003;
    padding: 0;
    width: 40%;
    height: 4px;
}

.device-container:hover .device-horizontal-strip {
    background-color: #4169e1;
    width: 30%;
}

.device-container-on:hover .device-horizontal-strip {
    margin-top: 15px;
    background-color: #0003;
    width: 20%;
}

.device-close-button-container {
    position: absolute;
    left: 10px;
    top: -5px;
    display: flex;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
}

.device-update-button-container {
    position: absolute;
    right: 10px;
    top: -5px;
    display: flex;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
}

.device-update-container,
.device-close-container {
    color: #4169e1;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 4px;
    user-select: none;
}

.device-update-container:hover,
.device-close-container:hover {
    transform: translateY(-1px);
}

.device-update-container:active,
.device-close-container:active {
    transform: scale(0.95);
}

.device-container-on .device-close-button-container,
.device-container-on .device-update-button-container {
    opacity: 1;
}

.device-container-on:hover .device-close-button-container,
.device-container-on:hover .device-update-button-container {
    opacity: 1;
}

.device-container-on:hover .device-update-container:hover,
.device-container-on:hover .device-close-container:hover {
    transform: translateY(-2px);
}

.device-container-on:hover .device-update-container:active {
    transform: translateY(0);
    color: #61c555;
}

.device-container-on:hover .device-close-container:active {
    transform: translateY(0);
    color: #e74c3c;
}

.icon_button,
.widget-button,
.button,
.mtz-name,
.button_max,
.button_show,
.device-item-n,
.device-item-s,
.precompiled,
.message-container,
.mtz-name-icon,
.open-setting {
    transition: all 0.3s ease !important;
}

/* 历史记录样式已移至 /src/styles/history.css */

/* 历史记录列表样式已移至 /src/styles/history.css */

.background-layer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    background-color: #4169e1;
    transform: translateY(0%);
    transition: all 0.3s ease;
    border-radius: 5px;
    opacity: 0;
    z-index: 0;
}

/* 历史记录文本容器和删除按钮样式已移至 /src/styles/history.css */

:root.window-blurred .icon_button:hover,
:root.window-blurred .widget-button:hover,
:root.window-blurred .button:hover,
:root.window-blurred .mtz-name:hover,
:root.window-blurred .button_max:hover,
:root.window-blurred .button_show:hover,
:root.window-blurred .device-item-n:hover,
:root.window-blurred .device-item-s:hover,
:root.window-blurred .precompiled:hover,
:root.window-blurred .message-container:hover {
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
}

:root.window-blurred .icon_button:hover .bg,
:root.window-blurred .button_show:hover::before {
    visibility: hidden !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease, visibility 0.3s ease !important;
}

:root.window-blurred .icon_button:hover .svgIcon {
    filter: none !important;
}

:root.window-blurred .icon_button:hover p {
    transform: none !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease, transform 0.3s ease !important;
}

:root.window-blurred .button_show:hover {
    background-color: #4169e1 !important;
    color: #fff !important;
}

:root.window-blurred .open-setting-container:hover .open-setting {
    background-color: #0005 !important;
    width: 40% !important;
    height: 4px !important;
}

:root.window-blurred .mtz-name-icon:hover {
    stroke: #4169e1;
    fill: #4169e1;
    box-shadow: none !important;
    background-color: #0001;
}

:root.window-blurred .button_max:hover+.precompiled {
    transform: none !important;
    opacity: 0 !important;
}

@keyframes gradientMove {
    0% {
        background-position: 400% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

#progress-container {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    font-size: 14px;
    font-family: system-ui, -apple-system, sans-serif;
}

@keyframes fadeIn {
    from {
        transform: translate(-50%, -10px);
        opacity: 0;
    }

    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

.button_max {
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.button_max::before {
    position: absolute;
    top: 0;
    left: 0;
    transform: translateX(-100%);
    z-index: -1;
    transition: transform 0.5s ease;
    background-color: #4169e1;
    width: 100%;
    height: 100%;
    pointer-events: none;
    content: attr(data-text);
}

.button_max.progressing {
    transition: all 0.5s ease;
}

.button_max.progressing::before {
    transform: translateX(var(--progress, -100%));
}

.button_max {
    position: relative;
    mix-blend-mode: difference;
    transition: all 0.3s ease;
    overflow: hidden;
}

.button_max span {
    position: relative;
    z-index: 1;
    color: #000;
}

.progress-bar {
    position: absolute;
    /* 从左侧开始 */
    top: 0;
    left: -100%;
    transform: translateX(0);
    /* 避免影响按钮点击 */
    z-index: 999999999999999;
    /* 初始位置 */
    transition: transform 0.3s ease-out;
    /* 固定宽度为100% */
    background-color: rgba(0, 0, 0, 1);
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.button_max:hover .progress-bar {
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.4));
}

.bubble-container {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    flex-direction: column;
    /* justify-content: center; */
    /* align-items: center; */
    text-align: center;
    /* gap: 6px; */
    z-index: 999999;
    background: #f0f0f0;
    padding: 0;
    margin: 0;
    width: 100vw;
    height: 100vh;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    transform: scale(1.2);
}

/* 泡泡的基础样式 */
.bubble {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    background-color: #fff;
    width: calc(100% - 20px);
    height: auto;
    overflow: hidden;
    font-size: 12px;
    margin: 10px;
    padding: 30px 0;
    border-radius: 6px;
    box-shadow: 0 0px 1px rgba(0, 0, 0, 0);
}

div.checkmarkcontainer {
    display: flex;
    justify-content: space-between;
    /* align-items: center; */
    font-size: 12px;
    color: #000a;
    background-color: #0001;
    width: 100%;
    height: 34px;
    border: none;
    padding: 5px 8px;
    box-sizing: border-box;
    gap: 5px;
    border-radius: 6px;
}

input[type="checkbox"] {
    position: absolute;
    right: 25px;
    /* top: 10px; */
    width: 16px;
    height: 16px;
    appearance: none;
    -webkit-appearance: none;
    border: 3px solid #0005;
    border-radius: 50%;
    background-color: transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-top: 4px;
    padding: 0;

    &:checked {
        background-color: #4169e1;
        border-color: #4169e1;

        /* 自定义勾选图标 */
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: block;
            border: solid white;
            border-radius: 50%;
            width: 0px;
            height: 0px;
        }
    }

    /* 悬停状态 */
    &:hover {
        border-color: #4169e1;
        box-shadow: 0 0 5px rgba(65, 105, 225, 0.3);
    }

    /* 禁用状态 */
    &:disabled {
        background-color: #e0e0e0;
        border-color: #ccc;
        cursor: not-allowed;
    }

    /* 聚焦状态 */
    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(65, 105, 225, 0.2);
    }
}

input {
    box-sizing: border-box;
    border: 1px solid #0003;
    border-radius: 6px;
    padding: 8px;
    width: 100%;
    height: 38px;
    color: #222;
    font-size: 13px;
    background-color: #0000;
}

textarea {
    box-sizing: border-box;
    margin-top: 5px;
    border: 1px solid #0003;
    border-radius: 6px;
    padding: 8px;
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    max-height: 70px;
    min-height: 70px;
    color: #222;
    font-size: 14px;
    background-color: #0000;
}

textarea:focus,
input:focus {
    /* 输入框选中时的边框颜色 */
    outline: 2px solid #4169e1;
    transition: all 0.3s ease-in-out;
}

input::placeholder {
    /* 输入框占位符文字颜色 */
    color: #000a;
    font-size: 13px;
}

textarea::placeholder {
    /* 文本框占位符文字颜色 */
    color: #000a;
    font-size: 13px;
}

.big_icon_button {
    margin: 5px 5px;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 12px;
    border: 1px solid #0003;
    background-color: #0000;
    transition: all 0.3s ease;
}

.big_icon_button:hover {
    padding: 8px 15px;
    border-radius: 5px;
    border: 1px solid #0000;
    background-color: #4169e1;
    color: #fff;
}

.big-icon-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    border-radius: 10px;
    transform: scale(0.8);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.big-icon-dialog-content {
    position: relative;
    background: #fff;
    padding: 50px 16px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 10px;
}

.form-group {
    margin-bottom: 10px;
}

.dialog-buttons {
    text-align: right;
}

.help-container {
    position: absolute;
    display: flex;
    flex-direction: column-reverse;
    flex-wrap: nowrap;
    align-content: space-around;
    align-items: center;
    justify-content: center;
    top: 0;
    right: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    pointer-events: none;
    opacity: 0;
}

.help-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    /* width: 80%; */
}

.help-circle {
    position: fixed;
    width: 0;
    height: 0;
    background: #ffffffcc;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.5s ease-out;
    pointer-events: none;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.help-link {
    display: inline-block;
    position: relative;
    color: #000;
    font-size: 12px;
    padding: 4px 0px;
    text-decoration: none;
    transition: all 0.5s ease-in-out;
    transform-origin: center;
    transform: scale(1) translateY(0);
    pointer-events: auto;
    opacity: 0.8;
}

.help-link::before {
    content: '';
    position: absolute;
    bottom: 6px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #4169e1;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    opacity: 0;
}

.help-link:hover {
    color: #4169e1;
    transform: scale(1.3) translateY(-2px) !important;
    opacity: 1;
    text-shadow: 0 2px 10px rgba(65, 105, 225, 0.15);
}

.help-link:hover::before {
    transform: scaleX(1);
    transform-origin: left;
    opacity: 0.5;
}

.help-link:active {
    transform: scale(0.95) translateY(0) !important;
    transition-duration: 0.15s;
}

.help-link:hover {
    color: #4169e1;
    transform: scale(1.1) translateY(-2px) !important;
    opacity: 1;
    text-shadow: 0 2px 10px rgba(65, 105, 225, 0.15);
}

.help-link:hover::before {
    transform: scaleX(1);
    transform-origin: left;
    opacity: 0.5;
}

.help-link:active {
    transform: scale(0.95) translateY(0) !important;
    transition-duration: 0.15s;
}

.tools-button-container {
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    transform: translateY(-5px);
}

.tools-button-container::-webkit-scrollbar {
    display: none;
}

.apply-theme-button-container {
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.apply-theme-button-container::-webkit-scrollbar {
    display: none;
}

.font-to-img {
    background-color: #ffffff;
    position: fixed;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99999;
    padding: 40px 10px 0px 10px;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.font-to-img-title {
    font-size: 12px;
    padding: 0px 10px 0px 10px;
    border: 1px solid #0003;
    background-color: #0000;
    border-radius: 10px;
    height: 40px;
    text-align: center;
    margin-bottom: 10px;
    margin-top: 40px;
    display: flex;
    /* justify-content: center; */
    align-items: center;
    text-align: center;
}

.font-content {
    background-color: #00000010;
    border-radius: 10px;
    display: flex;
    align-content: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    padding: 14px;
    font-size: 13px;
    margin-bottom: 5px;
}

.font-content-item {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.font-to-img-content {
    background-color: #0000;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 14px;
    padding: 20px 10px;
}

.img-container {
    display: flex;
    background-color: #00000005;
    border: 1px solid #0003;
    width: 36px;
    height: 36px;
    justify-content: center;
    align-items: center;
    padding: 2px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.img-container:hover {
    transform: translateY(-2px);
    background-color: #4169e1;
    color: #fff;
    border: 1px solid #fff3;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.img-container:active {
    transform: translateY(0);
    background-color: #4169e1;
    color: #fff;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0);
}

.img-container:hover .svgIcon {
    filter: invert(1);
    opacity: 1;
}

.preview-container {
    background-color: #0001;
    padding: 10px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    white-space: nowrap;
    font-size: 14px;
}

.font-input-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding-top: 10px;
}

.font-input {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
    height: 36px;
    border: 1px solid #0003;
    border-radius: 6px;
    padding: 10px;
    color: #222;
    font-size: 14px;
    background-color: #0000;
}

.font-input::placeholder {
    font-size: 12px;
}

.font-to-img-color {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding-top: 10px;
    gap: 10px;
}

.console-container {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 20px;
    background-color: #ff5f57;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999;
    color: #fff;
    font-size: 10px;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    cursor: pointer;
}

/* 添加悬停效果 */
.console-container:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}


.img-color-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    /* 单列布局 */
    grid-auto-rows: min-content;
    /* 行高自适应内容 */
    justify-items: center;
    /* 水平居中 */
    width: 100vw;
    overflow-x: auto;
    overflow-y: hidden;
    /* white-space: nowrap; */
    gap: 40px;
    /* 隐藏滚动条 */
    /* scrollbar-width: none; */
    /* -ms-overflow-style: none; */
}

.img-color-container::-webkit-scrollbar {
    display: none;
}

.img-color {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    background-color: #0000;
    width: 100%;
    /* 移除固定的宽高比，改为适应内容 */
    aspect-ratio: 1 / 2.222;
    border-radius: 10px;
    transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transform-origin: top;
    object-fit: contain;
    /* 确保图片内容不会被裁剪 */
    max-height: 60vh;
    /* 最大高度不超过视窗高度的60% */
    margin: 0 auto;
    /* 水平居中 */
}

.img-color:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    background-color: #0000;
}

.img-color:active {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    background-color: #0000;
    transform: scale(1);
}

/* 图片容器相关动画样式 */
.img-wrapper {
    transition: all 0.6s ease-in-out, transform 0.6s ease-in-out, opacity 0.6s ease, height 0.6s ease-in-out, margin 0.6s ease-in-out;
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
}


/* 添加截图按钮的动画效果 */
.add-screenshot-guide.add-button-entering {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

.add-screenshot-guide.add-button-moving {
    transition: opacity 0.3s ease, transform 0.6s ease-in-out, margin-top 0.5s ease-in-out;
    opacity: 0.5;
    pointer-events: none;
}

.add-screenshot-guide.add-button-entered {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: opacity 0.5s ease, transform 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.color-value {
    width: auto;
    height: auto;
    /* 改为自动高度以适应类型文本 */
    min-height: 36px;
    /* 设置最小高度 */
    background-color: #0000;
    border-radius: 50px;
    border: 1px solid #0003;
    padding: 7px 40px 7px 15px;
    /* 增加右侧内边距，为图标预留空间 */
    display: flex;
    align-items: center;
    font-size: 13px;
    transition: all 0.3s ease-in-out;
    font-family: "Fira Code", "Source Code Pro", "Consolas", "Monaco", "Menlo", "Ubuntu Mono", "DejaVu Sans Mono", monospace;
    position: relative;
    /* 确保子元素可以相对它定位 */
}

.color-value:hover {
    border: 1px solid #4169e1;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.color-value:active {
    border: 1px solid #4169e1;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0);
    transform: translateY(0);
}

.color-info {
    padding: 0px 10px 0px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.color-hex {
    font-size: 12px;
    color: #333;
}

.color-type-text {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
}

.color-value-pick {
    width: 12px;
    height: 12px;
    background-color: #0000ff;
    border-radius: 50px;
    border: 1px solid #0002;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    /* margin-right: 10px; */
    /* margin-left: 10px; */
}

.color-value-type {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.color-value-type-svg {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
    .color-value-type-svg {
        background-color: rgba(50, 50, 50, 0.7);
    }

    .color-hex {
        color: #eee;
    }
}

/* 使用提示 */
.add-screenshot-guide {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(249, 250, 255, 0.8);
    border: 2px dashed rgba(65, 105, 225, 0.25);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    position: fixed;
    top: 16%;
    left: 50%;
    transform: translate(-50%, -16%);
    overflow: hidden;
    box-shadow: 0 6px 16px rgba(65, 105, 225, 0.07);
}

.add-screenshot-guide .screenshot-text {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 30px;
    z-index: 2;
    text-align: left;
    font-family: "MiSans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    transform: translateY(0);
}


.add-screenshot-guide .screenshot-section {
    margin-bottom: 16px;
}



.add-screenshot-guide .screenshot-section-warning {
    font-size: 15px;
    font-weight: 500;
    color: rgba(235, 87, 87, 0.8);
    margin-bottom: 8px;
    letter-spacing: 0.3px;
}

.add-screenshot-guide .screenshot-list-item::before {
    content: "•";
    color: rgba(65, 105, 225, 0.7);
    font-weight: 600;
    display: inline-block;
    width: 16px;
    margin-right: 4px;
}

.add-screenshot-guide .screenshot-list-item,
.screenshot-warning-item {
    font-size: 14px;
    font-weight: 400;
    color: rgba(60, 60, 67, 0.75);
    margin-bottom: 6px;
    display: flex;
    align-items: flex-start;
    line-height: 1.45;
}

.add-screenshot-guide .screenshot-warning-item::before {
    content: "•";
    color: rgba(235, 87, 87, 0.7);
    font-weight: 600;
    display: inline-block;
    width: 16px;
    margin-right: 4px;
}

/* 侧边容器样式 */
.img-top-bar {
    /* padding: 0px 0px; */
    /* background: rgba(0, 0, 0, 0.05); */
    color: #000;
    z-index: 1;
    width: auto;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
    transition: top 0.5s ease;
    /* border-radius: 2rem; */
    -webkit-user-drag: none;
    -moz-user-drag: none;
    -ms-user-drag: none;
    user-select: none;
}

/* 序号样式 */
.img-index {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    font-size: 12px;
    margin: 0;
}

/* 删除按钮样式 */
.img-delete-btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    padding: 0;
    margin: 0;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    vertical-align: middle;
    text-align: center;
    border-radius: 2rem;
    transition: all 0.5s;
}

.img-delete-btn:hover {
    background-color: #fff;
}

.img-delete-btn img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    filter: invert(53%) sepia(95%) saturate(2233%) hue-rotate(325deg) brightness(98%) contrast(122%);
}

.edit_config_window {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(-180deg, rgba(96, 149, 245, 0.05) 0%, rgba(125, 175, 255, 0.1) 100%);
}



/* 类别选择器 */
.category-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e0e0e0;
}

.category-btn {
    padding: 8px 16px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.category-btn.active {
    /* background-color: #4169e1; */
    color: white;
    border-color: #4169e1;
}

.category-btn:hover {
    background-color: #f0f2f5;
}

.add-category-btn {
    padding: 8px 16px;
    background-color: #e6eeff;
    border: 1px dashed #4169e1;
    border-radius: 5px;
    color: #4169e1;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-category-btn:hover {
    background-color: #d6e4ff;
}

/* 内容编辑区 */
.content-editor {
    flex: 1;
    padding: 0px 20px 20px 20px;
    overflow-y: auto;
    margin-top: 70px;
}

/* 类别头部 */
.category-header {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 2px;
    margin-bottom: 20px;
    font-size: 12px;
}

.category-header h2 {
    margin: 0;
    color: #333;
}

/* 子类别容器 */
.subcategory-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

/* 子类别卡片 */
.subcategory-card {
    background-color: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.subcategory-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.item-count {
    color: #777;
    font-size: 13px;
}

/* 配置项编辑器 */
.editor-header {
    position: absolute;
    top: 10px;
    left: 25px;
    font-size: 12px;
    display: flex;
    align-items: center;
    z-index: 999999;
}

.back-btn {
    padding: 6px 12px;
    background-color: #f0f2f5;
    border: none;
    border-radius: 5px;
    margin-right: 15px;
    font-size: 14px;
    cursor: pointer;
}

.back-btn:hover {
    background-color: #e6e6e6;
}

.editor-header h2 {
    margin: 0;
    color: #333;
}

/* 配置项列表 */
.items-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
    width: 100%;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
}

/* 列表标题栏 */
.list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 15px;
    align-items: center;
    background-color: #f0f2f5;
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 1px solid #e6e6e6;
}

.header-item {
    color: #555;
    font-size: 14px;
}

/* 配置项卡片 */
.config-item-card {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 15px;
    align-items: center;
    background-color: #ffffff;
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.config-item-card:last-child {
    border-bottom: none;
}

.config-item-card:hover {
    background-color: #f9f9f9;
}

.field {
    display: flex;
    align-items: center;
}

.field.name-col input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.field.required-col select {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.field.group-col input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.field.action-col {
    display: flex;
    justify-content: flex-end;
}

/* 内容容器布局 */
.main-container {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

/* 左侧信息面板 */
.info-panel {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e6e6e6;
    align-self: flex-start;
    height: auto;
    position: sticky;
    top: 0;
}

.module-info,
.help-info,
.protected-info {
    margin-bottom: 20px;
}

.module-info h3,
.help-info h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #e6e6e6;
    padding-bottom: 8px;
}

.module-description p {
    margin: 8px 0;
    font-size: 14px;
}

.config-help-content ul {
    padding-left: 20px;
    margin: 0;
}

.config-help-content li {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;
}

/* 右侧配置面板 */
.config-panel {
    flex: 1;
}

.config-panel h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

/* 添加项目容器 */
.add-item-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}

/* 受保护内容样式 */
.protected-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff8e6;
    border: 1px solid #ffeeba;
    border-radius: 8px;
    padding: 15px;
    color: #856404;
    text-align: center;
}

.protected-icon {
    font-size: 24px;
    margin-bottom: 10px;
    line-height: 1;
}

.protected-info p {
    text-align: center;
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.protected-indicator {
    color: #856404;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.protected-indicator span {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    background-color: #fff8e6;
    border-radius: 50%;
    padding: 0;
    margin: 0;
    opacity: 1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 受保护卡片样式优化 */
.config-item-card.protected {
    background-color: #fffbf0;
    border-left: 3px solid #ffd970;
}

.config-item-card.protected input,
.config-item-card.protected select {
    background-color: #fffef7;
    border-color: #f0e8d0;
    color: #856404;
}

/* 按钮样式 */
.add-btn,
.save-btn {
    margin: 0 10px;
    padding: 10px 20px;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-btn,
.save-btn {
    background-color: #4169e1;
}

.add-btn:hover,
.save-btn:hover {
    background-color: #4169e1;
}

.delete-btn {
    padding: 8px 16px;
    background-color: #ff5252;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.delete-btn:hover {
    background-color: #e04848;
}

.delete-btn.small {
    padding: 6px 12px;
    font-size: 12px;
}

.edit-btn {
    padding: 8px 16px;
    background-color: #4169e1;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-btn:hover {
    background-color: #4169e1;
}

.edit-btn.small {
    padding: 6px 12px;
    font-size: 12px;
    margin-right: 5px;
}

/* 操作按钮区 */
.action-buttons {
    display: flex;
    justify-content: flex-end;
    padding: 20px 10px 20px 10px;
    /* background-color: #f5f7fa00; */
    /* border-top: 1px solid #e0e0e0; */
}

.edit_config_item {
    width: 50px;
    /* background-color: #3331; */
    display: flex;
    flex-direction: column;
    font-size: 9px;
    /* color: #000; */
    z-index: 1;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    opacity: 0.6;
    margin: 0;
    padding-top: 8px;
    transition: all 0.3s ease-in-out;
}

.edit_config_item:hover {
    /* background-color: #4169e17f; */
    /* color: white; */
    /* opacity: 1; */
    transform: scale(1.2);
}

.edit_config_item:active {
    /* background-color: #4169e1; */
    color: #4169e1;
    transform: scale(0.9);
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
    opacity: 1;
}

.edit_config_item:active .edit_config_icon {
    filter: invert(32%) sepia(90%) saturate(1000%) hue-rotate(210deg) brightness(95%) contrast(95%);
}

.edit_config_item.active {
    /* background-color: #4169e1; */
    color: #4169e1;
    /* transform: translateY(-2px); */
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
    opacity: 1;
}

.edit_config_icon {
    width: 20px;
    height: 20px;
    margin: 0;
    padding: 0;
    transition: all 0s ease-in-out;
}

.edit_config_icon.active {
    filter: invert(32%) sepia(90%) saturate(1000%) hue-rotate(210deg) brightness(95%) contrast(95%);
    /* width: 30px; */
    /* height: 30px; */
    margin: 0;
    padding: 0;
}

.protected-card,
.protected-content,
.protected {
    opacity: 0.7;
    pointer-events: none;
}

.protected-note.warning {
    background-color: #fff8e1;
    border-left: 4px solid #ffb300;
    padding: 10px;
    margin: 10px 0;
}

/* 配置编辑器主容器 */
.config-editor-main-content {
    width: 100%;
    /* padding: 0 20px 20px 20px; */
    box-sizing: border-box;
}

/* 受保护模式下的通知卡片 */
.protected-notice-card {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff8e6;
    border: 1px solid #ffeeba;
    border-radius: 8px;
    text-align: center;
    color: #856404;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.lock-icon {
    font-size: 36px;
    margin-bottom: 10px;
}

.protected-notice-card h3 {
    margin: 10px 0;
    font-size: 18px;
    color: #856404;
}

.protected-notice-card p {
    margin: 10px 0;
    font-size: 14px;
    line-height: 1.6;
}

/* 左右布局容器 */
.config-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    width: 100%;
}

/* 信息内容样式 */
.info-content {
    font-size: 14px;
}

/* 表格容器 */
.config-table-container {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    /* background-color: #fffc; */
    overflow: auto;
    /* max-height: calc(100vh - 10px); */
    /* 防止滚动影响布局 */
    overflow-anchor: none !important;
    scroll-behavior: auto !important;
    position: relative;
}

/* 表格样式 */
.config-table {
    width: 100%;
    border-collapse: separate;
    /* 修改为separate以确保边框模型正确 */
    border-spacing: 0;
    /* 确保单元格间没有间距 */
}

/* 表格头部 */
.config-table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    /* 提高z-index确保在其他元素之上 */
    background-color: #f5f7fa;
}

.config-table th {
    position: sticky;
    /* 让每个th也是sticky */
    top: 0;
    /* 与thead同样的位置 */
    padding: 12px 30px;
    text-align: left;
    /* font-weight: 600; */
    color: #555;
    border-bottom: 1px solid #e6e6e6;
    font-size: 14px;
    background-color: #f5f7fa;
    /* 确保背景色，防止内容透视 */
    /* box-shadow: 0 1px 0 #e6e6e6; */
    /* 添加底部阴影替代边框，解决滚动时边框消失问题 */
}

/* 表格内容 */
.config-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
    color: #333;
}

.config-table tr:hover {
    background-color: #f9f9f9;
}

.config-table tr:last-child td {
    border-bottom: none;
}

/* 表格单元格宽度控制 */
.config-table th:nth-child(1),
.config-table td:nth-child(1) {
    width: 40%;
}

.config-table th:nth-child(2),
.config-table td:nth-child(2) {
    width: 20%;
    text-align: center;
}

.config-table th:nth-child(3),
.config-table td:nth-child(3) {
    width: 15%;
    text-align: center;
}

.config-table th:nth-child(4),
.config-table td:nth-child(4) {
    width: 25%;
    text-align: center;
}

/* 配置表单 */
.config-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-input:focus {
    border-color: #4169e1;
    box-shadow: 0 0 0 2px rgba(65, 105, 225, 0.2);
}

.form-select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

.form-textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-height: 100px;
    resize: vertical;
}

/* 添加新的开关样式 */
/* 开关容器 */
.toggle-switch-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* 开关标签 */
.toggle-switch-label {
    font-size: 13px;
    color: #555;
    user-select: none;
    min-width: 16px;
    text-align: left;
}

/* 开关按钮 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 22px;
}

/* 隐藏默认复选框 */
.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

/* 开关滑块 */
.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease-in-out;
    border-radius: 22px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    top: 2px;
    /* 修改为使用top而不是bottom */
    background-color: white;
    transition: transform 0.3s ease;
    /* 简化过渡效果 */
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked+.toggle-slider {
    background-color: #4169e1;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

input:checked+.toggle-slider:before {
    transform: translateX(20px) translateY(0);
    /* 明确指定只有水平移动 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.toggle-switch:hover .toggle-slider {
    background-color: rgba(0, 0, 0, 0.25);
}

.toggle-switch:hover input:checked+.toggle-slider {
    background-color: #4169e1;
}

.toggle-switch-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0 auto;
    text-align: center;
    width: 100%;
}

.toggle-switch-label {
    font-size: 13px;
    color: #555;
    user-select: none;
    min-width: 16px;
    text-align: left;
}

/* 深色模式下的开关样式 */
@media (prefers-color-scheme: dark) {
    .toggle-slider {
        background-color: rgba(255, 255, 255, 0.2);
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .toggle-slider:before {
        background-color: #f0f0f0;
    }

    .toggle-switch-label {
        color: #ddd;
    }

    .toggle-switch:hover .toggle-slider {
        background-color: rgba(255, 255, 255, 0.3);
    }

    input:checked+.toggle-slider {
        background-color: #4169e1;
    }

    .toggle-switch:hover input:checked+.toggle-slider {
        background-color: #4169e1;
    }
}

/* 替换字段样式 */
.field.required-col {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 表格内容优化 */
.config-table td {
    vertical-align: middle;
    text-align: left;
}

.required-cell .toggle-switch-container {
    display: inline-flex;
    margin: 0 auto;
}

/* 针对组名单元格的特殊样式 */
.group-cell {
    text-align: center;
}

.group-cell input[type="text"] {
    text-align: center;
    min-width: 100%;
    margin: 0px;
}

/* 表格中的输入框样式 */
.config-table input[type="text"] {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.3s;
}

.special-mark {
    background-color: #ffe091;
    color: #856404;
    border-radius: 4px;
    padding: 4px 4px;
    font-size: 12px;
    justify-content: center;
    align-items: center;
    display: flex;
    margin-top: -5px;
}

/* 随机颜色主题配置编辑 */
.random_color_theme_edit_container {
    width: 100%;
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.config_section_title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.config_section_desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.config_edit_area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.json5_editor_container {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    overflow: hidden;
}

.json5_editor {
    width: 100%;
    height: 100%;
    min-height: 400px;
    padding: 15px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f9f9f9;
    border: none;
    resize: none;
    outline: none;
    box-sizing: border-box;
}

.config_buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

.config_button {
    padding: 8px 15px;
    background-color: #4169e1;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.config_button:hover {
    background-color: #4169e1;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config_button:active {
    transform: translateY(0);
    box-shadow: none;
}

/*
 * 随机颜色主题配置样式
 * 这些样式被用在set_random_color_theme_config.ts中
 */

/* 按钮基础样式 */
button.fixed-width-btn {
    width: 90px;
    min-width: 90px;
    max-width: 90px;
    box-sizing: border-box;
    padding: 4px 0;
    text-align: center;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    color: white;
    font-size: 13px;
    line-height: normal;
    display: inline-block;
    margin: 0;
    transition: background-color 0.2s;
    height: 32px;
    position: relative;
    vertical-align: middle;
}

/* 模块头部样式 */
.module-header {
    position: relative;
    height: 32px;
    margin-bottom: 14px;
    display: flex;
    align-items: center;
}

.module-header-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 100px);
    padding-right: 100px;
}

.header-button-area {
    position: absolute;
    right: 0;
    top: 0;
    width: 90px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 图片列表样式 */
.image-list-container {
    margin-bottom: 16px;
}

.image-list-title {
    font-size: 14px;
    font-weight: 500;
    color: #475569;
    margin-bottom: 10px;
}

.image-list {
    padding: 0;
    margin: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: #fff;
    list-style-type: none;
}

.image-list-empty {
    padding: 14px 16px;
    text-align: center;
    color: #64748b;
    font-size: 14px;
}

/* 图片列表项样式 */
.image-item {
    position: relative;
    min-height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
}

.image-item:not(:last-child) {
    border-bottom: 1px solid #e2e8f0;
}

.image-item:hover {
    background-color: #f1f5f9;
}

.image-text-container {
    padding: 10px 106px 10px 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    box-sizing: border-box;
}

.image-text {
    font-size: 14px;
    color: #334155;
}

.button-container {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 添加图片按钮容器 */
.add-btn-container {
    display: flex;
    justify-content: center;
    /* 改为居中显示 */
    align-items: center;
    height: 40px;
    position: relative;
}

/* 模块按钮容器 */
.module-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    margin-top: 10px;
}

/* 按钮样式 */
button.delete-module-btn,
button.delete-btn {
    background-color: #ff5252;
    /* 统一使用与状态列相同的样式 */
    color: white;
    border: none;
    border-radius: 5px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    min-width: 60px;
    height: 28px;
    transition: all 0.2s ease;
}

button.delete-module-btn:hover,
button.delete-btn:hover {
    background-color: #e04848;
}

/* 模块容器 */
.modules-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
}

/* 添加图片按钮样式 */
button.add-image-btn {
    background-color: #4169e1;
}

button.add-image-btn:hover {
    background-color: #365bd8;
}

/* 操作单元格样式 - 简化并保持关键属性 */
.config-table-cell-action {
    text-align: center !important;
    padding: 12px 10px !important;
}

/* 表格头部的状态列样式 */
.config-table-header-cell-action {
    text-align: center !important;
    padding: 12px 10px !important;
}

/* 确保操作按钮在表格中居中 */
.config-table .toggle-switch-container {
    display: block !important;
    margin: 0 auto !important;
    width: fit-content !important;
}

/* 确保第二列（状态列）所有内容居中 */
.config-table th:nth-child(2),
.config-table td:nth-child(2) {
    text-align: center !important;
    width: 20% !important;
}

/* 表格内容基本样式 */
.config-table td {
    vertical-align: middle;
    text-align: left;
}

/* 表格列样式 */
.config-table th:nth-child(1),
.config-table td:nth-child(1) {
    width: 70%;
    text-align: left;
}

.config-table th:nth-child(2),
.config-table td:nth-child(2) {
    width: 30%;
    text-align: center !important;
}

/* 表格状态列样式 */
.config-table-header-cell-action {
    text-align: center !important;
}

.config-table-cell-action {
    text-align: center !important;
}

/* 开关容器样式 */
.toggle-switch-container {
    margin: 0 auto;
    text-align: center;
    width: 100%;
}

/* 表格基础样式 */
.config-table td {
    vertical-align: middle;
}

/* 适配深色模式 */
@media (prefers-color-scheme: dark) {
    .content-container {
        background-color: #2a2a2a;
    }

    .info-panel {
        background-color: #333;
        border-color: #444;
    }

    .module-info h3,
    .help-info h3 {
        color: #e0e0e0;
        border-bottom-color: #444;
    }

    .list-header {
        background-color: #333;
        border-color: #444;
    }

    .header-item {
        color: #ddd;
    }

    .config-item-card {
        background-color: #2a2a2a;
        border-bottom-color: #444;
    }

    .config-item-card:hover {
        background-color: #343434;
    }

    .protected-info {
        background-color: #4d3800;
        border-color: #645200;
        color: #ffd970;
    }

    .config-item-card.protected {
        background-color: #3a3000;
        border-left-color: #ffd970;
    }

    .config-editor {
        background-color: #2b2b2b;
    }

    .category-selector {
        background-color: #1e1e1e;
        border-bottom-color: #444;
    }

    .category-btn {
        background-color: #333;
        border-color: #444;
        color: #ddd;
    }

    .category-btn:hover {
        background-color: #444;
    }

    .add-category-btn {
        background-color: #2a3245;
        border-color: #4169e1;
    }

    .add-category-btn:hover {
        background-color: #2e385a;
    }

    .category-header h2,
    .editor-header h2,
    .card-header h3 {
        color: #ddd;
    }

    .subcategory-card {
        background-color: #333;
        border-color: #444;
    }

    .item-count {
        color: #fff8;
    }

    .back-btn {
        background-color: #333;
        color: #ddd;
    }

    .back-btn:hover {
        background-color: #444;
    }

    .action-buttons {
        background-color: #1e1e1e;
        border-top-color: #444;
    }

    .items-list {
        background-color: #2a2a2a;
        border-color: #444;
    }

    .protected-indicator span {
        background-color: #4d3800;
        color: #ffd970;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .config-item-card.protected input,
    .config-item-card.protected select {
        background-color: #3a3000;
        border-color: #645200;
        color: #ffd970;
    }

    /* 深色模式下的滚动条样式 */

    .config-content::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .config-content::-webkit-scrollbar-track {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .config_section_title {
        color: #e0e0e0;
    }

    .config_section_desc {
        color: #aaa;
    }

    .json5_editor_container {
        border-color: #444;
        background-color: #2b2b2b;
    }

    .json5_editor {
        color: #ddd;
        background-color: #2b2b2b;
    }

    .config-table-container {
        border-color: #444;
        background-color: #2a2a2a;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .config-table thead {
        background-color: #333;
    }

    .config-table th {
        color: #ddd;
        border-bottom-color: #444;
        background-color: #333;
        /* 深色模式下的表头背景色 */
        box-shadow: 0 1px 0 #444;
        /* 深色模式下的表头底部阴影 */
    }

    .config-table td {
        color: #ccc;
        border-bottom-color: #444;
    }

    .config-table tr:hover {
        background-color: #343434;
    }

    .form-input,
    .form-select,
    .form-textarea {
        background-color: #333;
        border-color: #444;
        color: #ddd;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        border-color: #4169e1;
    }

    .form-label {
        color: #ddd;
    }

    /* 开关深色模式样式 */
    .toggle-slider {
        background-color: #555;
    }

    .toggle-switch-label {
        color: #ddd;
    }

    .toggle-slider:before {
        background-color: #ddd;
    }

    input:checked+.toggle-slider {
        background-color: #4169e1;
    }

    input:checked+.toggle-slider:before {
        background-color: white;
    }

    .toggle-slider {
        background-color: rgba(255, 255, 255, 0.15);
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .toggle-switch-label {
        color: #ddd;
    }

    .toggle-slider:before {
        background-color: #eee;
    }

    input:checked+.toggle-slider {
        background-color: #4169e1;
    }

    input:checked+.toggle-slider:before {
        background-color: white;
    }

    html,
    body {
        overflow: hidden !important;
        background-color: #232323;
        color: #f6f6f6;
    }

    .help-icon {
        filter: invert(1);
    }

    .mtz-name {
        box-shadow: #0001 0 4px 10px;
        border: 1px solid #fff1;
        background-color: #fff1;
    }

    .button_max {
        background-color: #fff1;
    }

    .widget-button,
    .button {
        border: 1px solid #fff2;
        color: #fff;
    }

    .widget-button:hover,
    .button_max:hover,
    .button:hover {
        border: 1px solid #fff2;
        box-shadow: #0005 0 8px 20px;
        background-color: #4169e1;
    }

    .widget-button:active,
    .button_max:active,
    .button:active {
        border: 1px solid #fff2;
        transform: translateY(0);
        box-shadow: none;
    }

    .message-container {
        background-color: #fff2;
        color: #fff;
    }

    svg {
        fill: #fff;
    }

    .svgIcon {
        filter: invert(1);
    }

    .icon_button {
        border: 1px solid #fff2;
        color: #fff;
    }

    .device-container {
        background-color: #333;
    }

    .device-container-on {
        border: 1px solid #fff1;
        background-color: #333;
        top: 70px;
    }

    .device-horizontal-strip {
        background-color: #fff3;
    }

    .device-container-on:hover .device-horizontal-strip {
        background-color: #fff3;
    }

    /* 历史记录暗色主题样式已移至 /src/styles/history.css */

    .device-item-s {
        border: 1px solid #fff3;
        background: #fff1;
        color: #fff;
    }

    .bubble-container-on {
        transform: translateY(0%);
        box-shadow: none;
        background: #0000;
    }

    /* 泡泡的基础样式 */
    .bubble {
        /* box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.15), -5px -5px 15px rgba(50, 50, 50, 0.8); */
        border: 1px solid #fff3;
        background: #ffffff10;
    }

    .bubble-hover {
        box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.15), -5px -5px 15px rgba(50, 50, 50, 0.8);
        background: #4169e1;
        color: #fff;
    }

    .mtz-name-icon {
        border: 1px solid #fff1;
        background-color: #fff1;
    }

    .big-icon-dialog-content {
        background: #232323;
    }

    input {
        border: 1px solid #fff3;
        color: #fff;
    }

    textarea {
        border: 1px solid #fff3;
        color: #fff;
    }

    input::placeholder {
        /* 输入框占位符文字颜色 */
        color: #fffa;
        font-size: 13px;
    }

    textarea::placeholder {
        /* 文本框占位符文字颜色 */
        color: #fffa;
        font-size: 13px;
    }

    input[type="checkbox"] {
        border: 3px solid #fff5;
    }

    .big_icon_button {
        border: 1px solid #fff3;
        color: #fff;
    }

    div.checkmarkcontainer {
        color: #fffa;
        background-color: #fff1;
    }

    .help-circle {
        background: #232323cc;
    }

    .help-link {
        color: #fffc;
        text-decoration: none;
    }

    .help-link:hover {
        color: #007aff;
    }

    .help-link::before {
        background: #007aff;
    }

    .help-link:hover {
        color: #007aff;
        text-shadow: 0 2px 10px rgba(0, 122, 255, 0.25);
    }

    .bubble-container {
        background: #232323;
    }

    .font-to-img {
        background-color: #232323;
    }

    .font-content {
        background-color: #ffffff09;
    }

    .img-container {
        display: flex;
        background-color: #ffffff09;
        border: 1px solid #fff3;
    }

    .img-container:hover {
        transform: translateY(-2px);
        background-color: #4169e1;
        color: #fff;
        border: 1px solid #fff3;
        box-shadow: 0 10px 20px #4169e17f;
    }

    .font-input {
        border: 1px solid #fff3;
    }

    .font-input::placeholder {
        font-size: 12px;
    }

    .img-color {
        box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }

    .img-color:hover {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        background-color: #0000;
    }

    .color-value {
        border: 1px solid #fff3;
    }

    .color-value:hover {
        border: 1px solid #4169e1;
        box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
        transform: translateY(-2px);
    }

    .color-value:active {
        border: 1px solid #4169e1;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0);
        transform: translateY(0);
    }

    .add-screenshot-guide {
        background-color: rgba(30, 34, 42, 0.6);
        border: 2px dashed rgba(96, 149, 245, 0.25);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }




    .add-screenshot-guide .screenshot-section-warning {
        color: rgba(235, 105, 105, 0.8);
    }

    .add-screenshot-guide .screenshot-list-item {
        color: rgba(220, 220, 230, 0.7);
    }

    .add-screenshot-guide .screenshot-list-item::before {
        color: rgba(96, 149, 245, 0.7);
    }

    .add-screenshot-guide .screenshot-warning-item {
        color: rgba(220, 220, 230, 0.7);
    }

    .add-screenshot-guide .screenshot-warning-item::before {
        color: rgba(235, 105, 105, 0.7);
    }

    .add-screenshot-guide:hover {
        background-color: rgba(35, 40, 50, 0.8);
        border-color: rgba(96, 149, 245, 0.5);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25), 0 0 1px rgba(96, 149, 245, 0.3);
    }





    .add-screenshot-guide:hover .screenshot-section-warning {
        color: rgba(235, 105, 105, 0.95);
    }

    .add-screenshot-guide::after {
        background: linear-gradient(135deg, rgba(96, 149, 245, 0.8) 0%, rgba(125, 175, 255, 0.8) 100%);
        color: rgba(20, 20, 30, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .add-screenshot-guide:hover::after {
        background: linear-gradient(135deg, rgba(96, 149, 245, 1) 0%, rgba(125, 175, 255, 1) 100%);
        box-shadow: 0 8px 20px rgba(96, 149, 245, 0.4);
    }

    .img-top-bar {
        background: rgba(255, 255, 255, 0.05);
        color: #fff;
    }

    .color-value-type-svg {
        color: #7aa4ff;
        /* 暗色模式下使用更亮的蓝色 */
    }

    .color-hex {
        color: #eee;
    }

    .color-type-text {
        color: #aaa;
    }
}

/* 表格中的按钮统一样式 */
.config-table-cell-action .delete-btn,
.config-table-cell-action .add-btn {
    display: inline-block !important;
    margin: 0 auto !important;
    /* 统一按钮尺寸 */
    min-width: 60px !important;
    height: 28px !important;
    font-size: 12px !important;
    padding: 6px 12px !important;
    box-sizing: border-box !important;
}

/* 确保所有小按钮样式统一 */
.delete-btn.small {
    min-width: 60px !important;
    height: 28px !important;
    font-size: 12px !important;
    padding: 6px 12px !important;
    box-sizing: border-box !important;
}

.config-table-cell-action {
    vertical-align: middle;
    text-align: center;
}

.delete-btn.small {
    margin: 0;
    vertical-align: middle;
    height: 28px;
}

.config-table-cell-action-wrapper {
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* 添加模块容器样式 */
.add-module-inline-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.add-module-input {
    flex: 1;
    /* height: 28px; */
    box-sizing: border-box;
}

/* 错误提示样式 */
.input-error {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.15) !important;
}

.empty-category-message {
    padding: 28px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 12px 0;
    font-size: 15px;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.03);
}

/* 整体类别面板容器 */
.category-panel {
    margin-bottom: 16px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    /* 确保相对定位，为内部元素提供定位上下文 */
}

.category-panel-header {
    display: flex;
    align-items: center;
    padding: 18px;
    background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    /* 确保相对定位 */
    z-index: 2;
    /* 确保在内容之上 */
}

.category-panel.expanded {
    box-shadow: 0 10px 30px rgba(65, 105, 225, 0.15);
    /* 移除transform属性以避免页面跳动 */
}

.category-panel.expanded .category-panel-header {
    background: linear-gradient(135deg, #eff3ff 0%, #e0e9ff 100%);
    border-bottom: 1px solid rgba(65, 105, 225, 0.2);
}

.category-name {
    flex: 1;
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #343a40;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.3s ease;
}

.category-panel.expanded .category-name {
    color: #4169e1;
    font-weight: 600;
}

.mapping-count {
    background: linear-gradient(135deg, #4169e1 0%, #4169e1 100%);
    border-radius: 30px;
    padding: 6px 14px;
    font-size: 14px;
    margin-right: 14px;
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(65, 105, 225, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    display: inline-block !important;
    /* 确保始终显示为内联块 */
    position: relative;
    /* 确保相对定位 */
    z-index: 3;
    /* 确保在最上层 */
}

.category-panel:hover .mapping-count {
    box-shadow: 0 4px 12px rgba(65, 105, 225, 0.3);
}

.category-panel.expanded .mapping-count {
    background: linear-gradient(135deg, #ffffff 0%, #f5f5ff 100%);
    color: #4169e1;
    box-shadow: 0 2px 6px rgba(65, 105, 225, 0.25);
    border: 1px solid rgba(65, 105, 225, 0.3);
    display: inline-block !important;
    /* 强制确保在展开时保持可见 */
    opacity: 1 !important;
    /* 强制确保完全不透明 */
    visibility: visible !important;
    /* 强制确保可见 */
    z-index: 5;
    /* 提高层级确保显示 */
}

/* 面板内容区域 */
.category-panel-content {
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    animation: slideDown 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    position: relative;
    /* 确保相对定位 */
    z-index: 1;
    /* 确保在头部之下 */
    transition: max-height 0.4s cubic-bezier(0.23, 1, 0.320, 1), opacity 0.4s cubic-bezier(0.23, 1, 0.320, 1), padding 0.4s cubic-bezier(0.23, 1, 0.320, 1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        /* 移除transform以避免页面跳动 */
    }

    to {
        opacity: 1;
        /* 移除transform */
    }
}

/* 深色模式下的面板样式 */
@media (prefers-color-scheme: dark) {
    .category-panel {
        background: linear-gradient(135deg, #333333 0%, #2a2a2a 100%);
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
    }

    .category-panel-header {
        background: linear-gradient(135deg, #333333 0%, #2d2d2d 100%);
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
        /* 确保相对定位 */
        z-index: 2;
        /* 确保在内容之上 */
    }

    .category-panel-header:hover {
        background: linear-gradient(135deg, #353535 0%, #31375b 100%);
    }

    .category-name {
        color: #ddd;
    }

    .category-panel.expanded {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.35);
    }

    .category-panel.expanded .category-panel-header {
        background: linear-gradient(135deg, #31375b 0%, #2d3452 100%);
        border-bottom: 1px solid rgba(65, 105, 225, 0.2);
    }

    .category-panel.expanded .category-name {
        color: #6d8ce8;
    }

    .category-panel-content {
        background: linear-gradient(135deg, #2a2a2a 0%, #252525 100%);
        position: relative;
        /* 确保相对定位 */
        z-index: 1;
        /* 确保在头部之下 */
        transition: max-height 0.4s cubic-bezier(0.23, 1, 0.320, 1), opacity 0.4s cubic-bezier(0.23, 1, 0.320, 1), padding 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    }

    .mapping-count {
        background: linear-gradient(135deg, #4169e1 0%, #4169e1 100%);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .category-panel.expanded .mapping-count {
        background: linear-gradient(135deg, #31375b 0%, #2d3452 100%);
        color: #8ca3eb;
        border: 1px solid rgba(109, 140, 232, 0.3);
        display: inline-block !important;
        /* 强制确保在展开时保持可见 */
        opacity: 1 !important;
        /* 强制确保完全不透明 */
        visibility: visible !important;
        /* 强制确保可见 */
        z-index: 5;
        /* 提高层级确保显示 */
    }
}

.add-category-container {
    display: flex;
    gap: 10px;
    margin-top: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #eaeaea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
}

.add-category-container:focus-within {
    border-color: #4169e1;
    box-shadow: 0 4px 12px rgba(65, 105, 225, 0.1);
}

.category-input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid #e0e4e8;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: white;
}

.category-input:focus {
    border-color: #4169e1;
    outline: none;
    box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.1);
}

/* 表格样式优化 */
.config-table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border: 1px solid #eaeaea;
    border-radius: 10px;
    overflow: hidden;
    /* margin-bottom: 20px; */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.config-table th {
    background: linear-gradient(to bottom, #f8f9fa, #f0f2f5);
    color: #495057;
    padding: 14px 16px;
    text-align: left;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid #eaeaea;
}

.config-table td {
    padding: 12px 16px;
    background-color: white;
    border-top: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.config-table tr:hover td {
    background-color: #f8f9fa;
}

.config-table-cell-value-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e0e4e8;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: white;
    font-size: 14px;
}

.config-table-cell-value-input:focus {
    border-color: #4169e1;
    outline: none;
    box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.1);
}

.config-table-add-row .config-table-cell-value-input {
    background-color: #f8f9fa;
    border: 1px solid #e0e4e8;
}

.config-table-add-row .config-table-cell-value-input:focus {
    background-color: white;
    border-color: #4169e1;
}

/* 按钮样式优化 */
.add-btn,
.save-btn,
.delete-btn {
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}

.add-btn {
    background: linear-gradient(to bottom, #4169e1, #4169e1);
    color: white;
}

.add-btn:hover {
    background: linear-gradient(to bottom, #4169e1, #2d4cb3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(65, 105, 225, 0.25);
}

.save-btn {
    background: linear-gradient(to bottom, #28a745, #218838);
    color: white;
}

.save-btn:hover {
    background: linear-gradient(to bottom, #218838, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.25);
}

.save-btn.unsaved {
    background: linear-gradient(to bottom, #ffc107, #e0a800);
    color: #212529;
}

.save-btn.unsaved:hover {
    background: linear-gradient(to bottom, #e0a800, #d39e00);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.25);
}

.delete-btn {
    background: linear-gradient(to bottom, #dc3545, #c82333);
    color: white;
}

.delete-btn:hover {
    background: linear-gradient(to bottom, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.25);
}

.delete-btn.small,
.add-btn.small {
    padding: 8px 12px;
    font-size: 13px;
}

/* 适配深色模式 */
@media (prefers-color-scheme: dark) {
    .empty-category-message {
        background-color: #333;
        color: #aaa;
        box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.15);
    }

    .category-panel {
        background: #333;
        border-color: #444;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .category-panel:hover {
        border-color: #555;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
    }

    .category-panel-header {
        background: linear-gradient(to right, #2a2a2a, #333);
        color: #ddd;
    }

    .category-panel-header:hover {
        background: linear-gradient(to right, #333, #3a3a3a);
    }

    .toggle-btn {
        color: #6d8ce8;
    }

    .toggle-btn:hover {
        background-color: rgba(109, 140, 232, 0.15);
    }

    .category-name {
        color: #ddd;
    }

    .mapping-count {
        background-color: #4169e1;
        color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .category-panel-content {
        background-color: #333;
        border-top-color: #444;
    }

    .add-category-container {
        background-color: #2a2a2a;
        border-color: #444;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .add-category-container:focus-within {
        border-color: #4169e1;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    }

    .category-input {
        background-color: #333;
        border-color: #444;
        color: #ddd;
    }

    .category-input:focus {
        border-color: #4169e1;
        box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.15);
    }

    .config-table {
        border-color: #444;
        background-color: #333;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .config-table th {
        background: linear-gradient(to bottom, #2a2a2a, #333);
        color: #ddd;
        border-bottom-color: #444;
    }

    .config-table td {
        background-color: #333;
        border-top-color: #444;
        color: #ddd;
    }

    .config-table tr:hover td {
        background-color: #3a3a3a;
    }

    .config-table-cell-value-input {
        background-color: #333;
        border-color: #444;
        color: #ddd;
    }

    .config-table-cell-value-input:focus {
        border-color: #4169e1;
        box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.15);
    }

    .config-table-add-row .config-table-cell-value-input {
        background-color: #2a2a2a;
        border-color: #444;
    }

    .config-table-add-row .config-table-cell-value-input:focus {
        background-color: #333;
        border-color: #4169e1;
    }
}

/* 额外美化字体映射列表的样式 */
.category-panel.expanded {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 深色模式下展开的面板 */
@media (prefers-color-scheme: dark) {
    .category-panel.expanded {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }
}

/* 为表格添加条纹效果 */
.config-table tr:nth-child(odd) td {
    background-color: #f8f9fa;
}

.config-table tr:nth-child(even) td {
    background-color: #ffffff;
}

/* 深色模式下的表格条纹 */
@media (prefers-color-scheme: dark) {
    .config-table tr:nth-child(odd) td {
        background-color: #2d2d2d;
    }

    .config-table tr:nth-child(even) td {
        background-color: #333333;
    }
}

/* 添加帮助信息的美化 */

/* 深色模式下的帮助项样式 */

/* 添加搜索功能的样式 */
.category-search-container {
    margin-bottom: 16px;
    position: relative;
}

.category-search-input {
    width: 100%;
    padding: 10px 14px 10px 40px;
    border: 1px solid #e0e4e8;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.category-search-input:focus {
    border-color: #4169e1;
    outline: none;
    box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.1);
}

.search-icon {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 16px;
}

/* 深色模式搜索样式 */
@media (prefers-color-scheme: dark) {
    .category-search-input {
        background-color: #333;
        border-color: #444;
        color: #ddd;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    .category-search-input:focus {
        border-color: #4169e1;
        box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.15);
    }

    .search-icon {
        color: #aaa;
    }
}

/* 类别摘要信息样式 */
.category-summary {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px dashed #eaeaea;
}

.sample-info {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #4169e1;
}

.sample-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.sample-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.sample-item {
    font-size: 13px;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.sample-key {
    color: #4169e1;
    font-weight: 500;
    margin-right: 4px;
    padding: 2px 6px;
    background-color: rgba(65, 105, 225, 0.1);
    border-radius: 4px;
    font-family: monospace;
}

.sample-value {
    color: #495057;
    padding: 2px 6px;
    background-color: #ffffff;
    border-radius: 4px;
    font-family: monospace;
}

.more-items {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
    margin-top: 4px;
}

/* 深色模式下的摘要样式 */
@media (prefers-color-scheme: dark) {
    .category-summary {
        border-bottom-color: #444;
    }

    .sample-info {
        background-color: #2a2a2a;
        border-left-color: #4169e1;
    }

    .sample-label {
        color: #ddd;
    }

    .sample-item {
        color: #aaa;
    }

    .sample-key {
        color: #6d8ce8;
        background-color: rgba(109, 140, 232, 0.15);
    }

    .sample-value {
        color: #ddd;
        background-color: #333;
    }

    .more-items {
        color: #999;
    }
}

/* 更新左侧信息栏样式 */

/* 深色模式下的合并卡片样式 */

/* 优化开关按钮样式 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    transition: all 0.3s ease;
}

/* 隐藏默认复选框 */
.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

/* 开关轨道 */
.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 24px;
    transition: background-color 0.3s ease;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    margin: 0;
    /* 确保没有边距 */
    padding: 0;
    /* 确保没有内边距 */
}

/* 开关滑块 */
.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    top: 2px;
    background-color: white;
    transition: transform 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    will-change: transform;
    /* 优化变换性能 */
}

/* 开关激活状态 */
input:checked+.toggle-slider {
    background-color: #4169e1;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

input:checked+.toggle-slider:before {
    transform: translateX(20px);
    /* 只使用X轴变换 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 开关容器样式 */
.toggle-switch-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0;
    text-align: center;
    width: 100%;
    height: 34px;
    /* 固定高度 */
    box-sizing: border-box;
}

/* 开关标签样式 */
.toggle-switch-label {
    font-size: 13px;
    color: #555;
    user-select: none;
    min-width: 16px;
    text-align: left;
}

/* 开关悬停状态 */
.toggle-switch:hover .toggle-slider {
    background-color: rgba(0, 0, 0, 0.2);
}

.toggle-switch:hover input:checked+.toggle-slider {
    background-color: #4169e1;
}

/* 开关聚焦状态 */
input:focus+.toggle-slider {
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(65, 105, 225, 0.1);
}

input:checked:focus+.toggle-slider {
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(65, 105, 225, 0.2);
}

/* 开关禁用状态 */
.toggle-switch.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.toggle-switch.disabled .toggle-slider {
    cursor: not-allowed;
}

/* 开关容器样式 */
.toggle-switch-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0;
    text-align: center;
    width: 100%;
}

/* 开关标签样式 */
.toggle-switch-label {
    font-size: 13px;
    color: #555;
    user-select: none;
    min-width: 16px;
    text-align: left;
}

/* 深色模式下的开关样式 */
@media (prefers-color-scheme: dark) {
    .toggle-slider {
        background-color: rgba(255, 255, 255, 0.15);
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .toggle-slider:before {
        background-color: #f0f0f0;
    }

    .toggle-switch-label {
        color: #ddd;
    }

    .toggle-switch:hover .toggle-slider {
        background-color: rgba(255, 255, 255, 0.25);
    }

    input:checked+.toggle-slider {
        background-color: #4169e1;
    }

    .toggle-switch:hover input:checked+.toggle-slider {
        background-color: #4169e1;
    }

    input:focus+.toggle-slider {
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(65, 105, 225, 0.2);
    }

    input:checked:focus+.toggle-slider {
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(65, 105, 225, 0.3);
    }
}

/* 现代化开关样式 - 全新设计 */
.modern-toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    /* 固定高度 */
    margin: 0;
    /* 移除上下外边距 */
    padding: 0;
    /* 确保没有内边距 */
}

.modern-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

/* 开关轨道 */
.modern-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e0e0e0;
    border-radius: 30px;
    transition: background-color 0.3s ease;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

/* 开关滑块 */
.modern-toggle-slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 3px;
    top: 3px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 2;
    will-change: transform;
    /* 优化变换性能 */
}

/* 开关打开状态 */
input:checked+.modern-toggle-slider {
    background-color: #4169e1;
}

input:checked+.modern-toggle-slider:before {
    transform: translateX(30px) translateZ(0);
    /* 只使用水平变换 */
}

/* 开关内部元素 - 图标 */
.modern-toggle-slider:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 35px;
    width: 10px;
    height: 10px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='14' height='14'%3E%3Cpath d='M9,16.17L4.83,12l-1.42,1.41L9,19 21,7l-1.41-1.41L9,16.17z' fill='rgba(255,255,255,0.7)'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    /* 禁止鼠标事件 */
}

input:checked+.modern-toggle-slider:after {
    opacity: 0.7;
}

/* 改进开关容器样式 */
.modern-toggle-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    margin: 0;
    text-align: center;
    width: 100%;
    height: 46px;
    /* 固定高度 */
    box-sizing: border-box;
    /* 确保边框和内边距不会增加元素尺寸 */
}

.modern-toggle-label {
    font-size: 14px;
    color: #444;
    font-weight: 500;
    user-select: none;
    min-width: 16px;
    text-align: left;
    transition: color 0.2s ease;
}

/* 当开关打开时的标签颜色变化 */
input:checked~.modern-toggle-label {
    color: #4169e1;
}

/* 深色模式下的现代开关样式 */
@media (prefers-color-scheme: dark) {
    .modern-toggle-slider {
        background-color: #3a3a3a;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    }

    .modern-toggle-slider:before {
        background-color: #f0f0f0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    .modern-toggle-label {
        color: #ddd;
    }

    .modern-toggle-switch:hover .modern-toggle-slider {
        background-color: #444;
    }

    input:checked+.modern-toggle-slider {
        background-color: #4169e1;
    }

    .modern-toggle-switch:hover input:checked+.modern-toggle-slider {
        background-color: #2d4cb3;
    }

    input:focus+.modern-toggle-slider {
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(65, 105, 225, 0.3);
    }

    input:checked:focus+.modern-toggle-slider {
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(65, 105, 225, 0.4);
    }

    input:checked~.modern-toggle-label {
        color: #6d8ce8;
    }
}

/* 确保操作按钮在表格中居中 */
.config-table .modern-toggle-container {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto !important;
    width: fit-content !important;
}

/* 新的UI开关样式 - 基于ozgeozkaraa01的设计 */
.ui-toggle-switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 17px;
    transition: none;
    /* 移除过渡效果，避免意外位移 */
    margin: 0;
    /* 确保没有边距 */
    padding: 0;
    /* 确保没有内边距 */
}

.ui-toggle-input {
    display: none;
}

.ui-toggle-label {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 17px;
    border-radius: 8.5px;
    background: linear-gradient(to bottom, #bbb, #ccc);
    cursor: pointer;
    transition: background 0.3s ease;
    margin: 0;
    /* 确保没有边距 */
    padding: 0;
    /* 确保没有内边距 */
}

.ui-toggle-label:before {
    content: '';
    position: absolute;
    top: 1.5px;
    left: 1.5px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
    will-change: transform;
    /* 优化变换性能 */
}

.ui-toggle-input:checked+.ui-toggle-label {
    background: linear-gradient(to bottom, #28a745, #218838);
}

.ui-toggle-input:checked+.ui-toggle-label:before {
    transform: translateX(13px) translateZ(0);
    /* 只使用X轴变换 */
}

/* 深色模式下的UI开关样式 */
@media (prefers-color-scheme: dark) {
    .ui-toggle-label {
        background-color: #555;
    }

    .ui-toggle-label:before {
        background-color: #f0f0f0;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .ui-toggle-input:checked+.ui-toggle-label {
        background: linear-gradient(to bottom, #28a745, #218838);
    }
}

/* UI开关容器样式 */
.ui-toggle-container {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0;
    text-align: center;
    /* width: 100%; */
    height: 25px;
    /* 固定高度 */
    box-sizing: border-box;
}

.ui-toggle-text {
    font-size: 12px;
    line-height: 12px;
    color: #444;
    font-weight: 500;
    user-select: none;
    min-width: 14px;
    text-align: left;
    transition: color 0.2s ease;
}

@media (prefers-color-scheme: dark) {
    .ui-toggle-text {
        color: #ddd;
    }
}

/* 确保表格中的UI开关居中 */
.config-table .ui-toggle-container {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto !important;
    width: fit-content !important;
}

/* 禁用状态 */
.ui-toggle-switch.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.ui-toggle-switch.disabled .ui-toggle-label {
    cursor: not-allowed;
}

/* 删除所有重复的样式 */

/* 确保各组件在表格内固定高度 */
.config-table .toggle-switch-container,
.config-table .modern-toggle-container,
.config-table .ui-toggle-container {
    height: auto !important;
    min-height: 34px !important;
    max-height: 34px !important;
    overflow: visible !important;
    transform: translateZ(0);
    /* 强制硬件加速，防止渲染问题 */
}

/* 统一各种开关组件的变换效果 */
.toggle-slider:before,
.modern-toggle-slider:before,
.ui-toggle-label:before {
    backface-visibility: hidden;
    /* 防止3D变换导致的渲染问题 */
    transform-style: preserve-3d;
    /* 确保3D变换的一致性 */
    will-change: transform;
    /* 提示浏览器此元素将发生变换，优化性能 */
}

/* 确保所有容器在状态变化时尺寸不变 */
.toggle-switch-container,
.modern-toggle-container,
.ui-toggle-container {
    transform: translateZ(0);
    /* 强制硬件加速 */
    contain: layout;
    /* 如果浏览器支持，隔离布局变化 */
}

/* 在Firefox中修复可能的渲染问题 */
@-moz-document url-prefix() {

    .toggle-slider:before,
    .modern-toggle-slider:before,
    .ui-toggle-label:before {
        transform: translateX(0) translateZ(0);
    }

    input:checked+.toggle-slider:before {
        transform: translateX(20px) translateZ(0);
    }
}

/* 用于防止面板展开/收起时的页面跳动 */
.panel-container {
    position: relative;
    margin-bottom: 16px;
    transition: min-height 0.4s ease, height 0.4s ease;
    overflow: visible;
    /* 允许内容溢出，防止截断 */
    z-index: 1;
    /* 确保正常的层叠顺序 */
}

/* 新增行样式 */
.new-item-row {
    background-color: #f9f9f9;
    border-top: 1px dashed #ddd;
    transition: background-color 0.3s ease;
}

.new-item-row:hover {
    background-color: #f0f0f0;
}

.new-item-input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    transition: all 0.3s ease;
}

.new-item-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(65, 105, 225, 0.2);
}

.new-item-input::placeholder {
    color: #999;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .new-item-row {
        background-color: #2a2a2a;
        border-top: 1px dashed #444;
    }

    .new-item-row:hover {
        background-color: #333;
    }

    .new-item-input {
        background-color: #333;
        border-color: #444;
        color: #e0e0e0;
    }

    .new-item-input:focus {
        border-color: var(--primary-color-light);
        box-shadow: 0 0 0 2px rgba(65, 105, 225, 0.3);
    }

    .new-item-input::placeholder {
        color: #777;
    }
}

/* 确保添加按钮使用蓝色 */
.add-btn.small {
    /* background-color: #4169e1 !important; */
    color: white !important;
    border: none !important;
    border-radius: 5px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    min-width: 60px !important;
    height: 28px !important;
    box-sizing: border-box !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin: 0 !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
}

.add-btn.small:hover {
    background-color: #4169e1 !important;
}

@media (prefers-color-scheme: dark) {
    .add-btn.small {
        background-color: #4169e1 !important;
    }

    .add-btn.small:hover {
        background-color: #4169e1 !important;
    }
}

p2 {
    font-size: 15px;
}

.version-content {
    flex: 1;
    /* padding: 16px; */
    overflow-y: auto;
}

.module-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.module-card {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.module-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--hover-color);
    border-bottom: 1px solid var(--border-color);
}

.module-card-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
}

.item-count {
    font-size: 12px;
    opacity: 0.7;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.item-row:last-child {
    border-bottom: none;
}

.item-row:hover {
    background-color: var(--hover-color);
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 4px;
}

.item-detail {
    font-size: 12px;
    color: var(--text-color);
    opacity: 0.7;
}

.item-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .version-nav-btn {
        color: var(--text-color);
        border-color: var(--border-color);
    }

    .version-nav-btn:hover {
        background-color: var(--hover-color);
    }

    .version-nav-btn.active {
        background-color: var(--primary-color);
        color: white;
    }

    .module-card {
        background-color: var(--background-color);
        border-color: var(--border-color);
    }

    .module-card-header {
        background-color: var(--hover-color);
        border-color: var(--border-color);
    }

    .item-row {
        border-color: var(--border-color);
    }

    .item-row:hover {
        background-color: var(--hover-color);
    }
}

/* 图片裁剪配置界面样式 */
.version-action-area {
    display: flex;
    justify-content: flex-end;
    padding: 0 16px 16px 16px;
    margin-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.add-item-dialog .cancel-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: transparent;
    color: var(--text-color);
    cursor: pointer;
}

.add-item-dialog .add-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
}

.add-item-dialog .add-btn:hover {
    background-color: var(--primary-color-dark);
}

.add-item-dialog .cancel-btn:hover {
    background-color: var(--hover-color);
}

.empty-state {
    padding: 16px;
    text-align: center;
    color: #999;
    font-style: italic;
    border-top: 1px dashed #ddd;
}

/* 模块头部区域样式 */
.module-header-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.module-area-title {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
}

/* 模块卡片标题栏样式 */
.module-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--hover-color);
    border-bottom: 1px solid var(--border-color);
}

.module-card-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
}

.module-card-header .add-btn.small {
    padding: 4px 10px;
    font-size: 12px;
}

.config-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 指南面板样式 */
.guide-panel {
    margin-bottom: 24px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.guide-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.guide-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.guide-toggle-btn {
    background-color: transparent;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
}

.guide-content {
    padding: 16px;
    background-color: #fff;
}

.guide-step {
    display: flex;
    margin-bottom: 16px;
}

.step-number {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    background-color: #4169e1;
    color: white;
    border-radius: 50%;
    font-weight: bold;
    margin-right: 12px;
    flex-shrink: 0;
}

.step-content {
    flex-grow: 1;
}

.step-content h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.step-content p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #555;
}

.step-example {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    color: #333;
    border-left: 3px solid #4169e1;
}

/* 配置区域样式 */
.config-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.section-header .toggle-wrapper {
    position: relative;
    border: none;
    padding: 0;
    margin: 0;
}

.section-header .toggle-container {
    width: auto;
    display: flex;
    align-items: center;
    position: relative;
    border: 1px solid rgba(65, 105, 225, 0.3);
    padding: 2px;
    border-radius: 20px;
    background-color: rgba(65, 105, 225, 0.05);
}

.section-header .toggle-input {
    width: 40px;
    height: 20px;
    margin: 0;
    position: relative;
    appearance: none;
    background-color: transparent;
    border-radius: 20px;
    transition: background-color 0.3s;
    cursor: pointer;
    z-index: 1;
}

.section-header .toggle-slider {
    width: 40px;
    height: 20px;
    right: 2px;
    top: 2px;
    position: absolute;
    border-radius: 20px;
    pointer-events: none;
    z-index: 0;
}

.section-content {
    padding: 16px;
    background-color: #fff;
}

/* 输入字段样式 */
.input-field-container {
    margin-bottom: 16px;
}

.input-field-container:last-child {
    margin-bottom: 0;
}

.input-field-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s;
}

.input-field-control:focus {
    border-color: #4169e1;
    outline: none;
    box-shadow: 0 0 0 2px rgba(65, 105, 225, 0.15);
}

.input-field-help {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* 字段列表样式 */
.fields-list-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
}

/* 字段全宽容器 */
.field-full-width {
    width: 100%;
}

/* 坐标和尺寸容器 */
.position-size-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    width: 100%;
}

.position-size-container .input-field-container {
    flex: 1;
    min-width: 100px;
}

@media (max-width: 768px) {
    .fields-list-container {
        grid-template-columns: 1fr;
    }
}

/* 点9图相关样式 */
.nine-patch-config {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 添加toggle-wrapper样式 */
.toggle-wrapper {
    /* width: 100%; */
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 5px;
}

.toggle-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: fit-content;
    position: relative;
}

.toggle-label {
    font-weight: bold;
    color: #333;
    margin-right: 8px;
}

.toggle-input {
    position: relative;
    width: 40px;
    height: 20px;
    appearance: none;
    background-color: #ccc;
    border-radius: 20px;
    transition: background-color 0.3s;
    cursor: pointer;
    margin: 0;
    z-index: 1;
}

.toggle-input:checked {
    background-color: #4169e1;
}

.toggle-slider {
    position: absolute;
    width: 40px;
    height: 20px;
    right: 8px;
    /* 考虑到标签右边的间距 */
    top: 0;
    border-radius: 20px;
    pointer-events: none;
    z-index: 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-input:before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    background-color: white;
    transition: transform 0.3s;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked:before {
    transform: translateX(20px);
}

/* 深色模式下的开关样式 */
@media (prefers-color-scheme: dark) {

    /* 深色模式下的toggle-wrapper样式 */
    .toggle-wrapper {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .toggle-label {
        color: #f0f0f0;
    }

    .toggle-input {
        background-color: #444;
    }

    .toggle-slider {
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .toggle-input:before {
        background-color: #f0f0f0;
    }
}

.nine-patch-explainer {
    display: flex;
    justify-content: center;
    padding: 8px 0;
}

.nine-patch-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.nine-patch-demo-image {
    width: 240px;
    height: 160px;
    background-color: white;
    border: 2px solid #333;
    position: relative;
}

.nine-patch-stretch-x {
    position: absolute;
    left: 60px;
    top: 0;
    width: 120px;
    height: 160px;
    background-color: rgba(0, 150, 255, 0.2);
    z-index: 1;
}

.nine-patch-stretch-y {
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    background-color: rgba(0, 150, 255, 0.2);
    z-index: 1;
}

.nine-patch-content {
    position: absolute;
    left: 60px;
    top: 40px;
    width: 120px;
    height: 80px;
    background-color: rgba(255, 200, 0, 0.3);
    border: 1px solid rgba(255, 150, 0, 0.5);
    z-index: 2;
}

.nine-patch-border-top,
.nine-patch-border-left,
.nine-patch-border-right,
.nine-patch-border-bottom {
    position: absolute;
    background-color: #333;
}

.nine-patch-border-top {
    top: -5px;
    left: 60px;
    width: 120px;
    height: 2px;
}

.nine-patch-border-left {
    left: -5px;
    top: 40px;
    width: 2px;
    height: 80px;
}

.nine-patch-border-right {
    right: -5px;
    top: 40px;
    width: 2px;
    height: 80px;
}

.nine-patch-border-bottom {
    bottom: -5px;
    left: 60px;
    width: 120px;
    height: 2px;
}

.nine-patch-legend {
    display: flex;
    gap: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.color-block {
    width: 16px;
    height: 16px;
    border-radius: 2px;
}

.color-block.stretch {
    background-color: rgba(0, 150, 255, 0.2);
    border: 1px solid rgba(0, 100, 255, 0.4);
}

.color-block.content {
    background-color: rgba(255, 200, 0, 0.3);
    border: 1px solid rgba(255, 150, 0, 0.5);
}

.color-block.border {
    background-color: #333;
}

.nine-patch-fields {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.disabled-fields {
    opacity: 0.5;
    pointer-events: none;
}

/* 数组输入样式 */
.array-field-container {
    margin-bottom: 16px;
}

.array-field-container:last-child {
    margin-bottom: 0;
}

.array-field-label {
    display: block;
    font-weight: bold;
    margin-bottom: 6px;
    color: #333;
}

.array-field-inputs {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
}

.array-field-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.array-field-input:focus {
    border-color: #4169e1;
    outline: none;
}

.array-field-help {
    font-size: 12px;
    color: #666;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .add-module-btn {
        background-color: #333;
        color: #f0f0f0;
    }

    .add-module-btn:hover {
        background-color: #444;
    }

    .module-card-header {
        background-color: #333;
    }

    .guide-panel,
    .config-section {
        background-color: #232323;
        border-color: #444;
    }

    .guide-header,
    .section-header {
        background-color: #232323;
        border-color: #444;
    }

    .guide-header h4,
    .section-header h4 {
        color: #f0f0f0;
    }

    .guide-content,
    .section-content {
        color: #ddd;
        background-color: #232323;
    }

    .step-content p {
        color: #ccc;
    }

    .step-example {
        color: #bbb;
        background-color: #333;
    }

    .input-field-control,
    .array-field-input {
        background-color: #333;
        border-color: #555;
        color: #f0f0f0;
    }

    .input-field-help,
    .array-field-help {
        color: #aaa;
    }

    .nine-patch-demo-image {
        background-color: #444;
        border-color: #666;
    }

    /* 深色模式下的toggle-wrapper样式 */
    .toggle-wrapper {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .toggle-label {
        color: #f0f0f0;
    }
}

.toggle-container {
    display: flex;
    align-items: center;
    position: relative;
    width: fit-content;
    margin-left: auto;
    gap: 8px;
    margin-right: 20px;
}

.toggle-status {
    font-size: 12px;
    color: #666;
    min-width: 32px;
    text-align: right;
}

.toggle-input {
    position: relative;
    width: 38px;
    height: 20px;
    appearance: none;
    background-color: #ccc;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin: 0;
    border: none;
    outline: none;
}

.toggle-input:checked {
    background-color: #5cb85c;
}

.toggle-slider {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    pointer-events: none;
}

.toggle-input:before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: white;
    left: 2px;
    top: 2px;
    transition: transform 0.2s ease;
}

.toggle-input:checked:before {
    transform: translateX(18px);
}

/* 加深一下暗色模式下的颜色 */
@media (prefers-color-scheme: dark) {
    .toggle-input {
        background-color: #444;
    }

    .toggle-input:checked {
        background-color: #4a994a;
    }

    .toggle-status {
        color: #999;
    }

    /* 表格中的输入框样式 */
    .config-table input[type="text"] {
        border: 1px solid #fff2;
    }

    .edit_config_icon {
        filter: invert(1);
    }
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========== 设备选择弹窗样式 - 来自special_overlay_button.css ========== */

/* 现代化覆盖层 - 使用flexbox确保完美居中 */
.special-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99998;

    /* 简洁的背景效果 */
    background: rgba(248, 248, 248, 0.85);
    backdrop-filter: blur(20px) saturate(120%);

    /* 使用flexbox实现完美居中 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;

    /* 确保内容不会超出屏幕 */
    box-sizing: border-box;
    overflow: hidden;

    /* 优雅的淡入动画 */
    animation: overlayFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 按钮容器 - 简化布局，依赖父容器的flexbox居中 */
.special-button-container {
    width: 240px;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 20px;
    padding: 20px 0;
}

/* 覆盖层标题 */
.overlay-title {
    margin: 0 5% 10px 5%;
    font-size: 11px;
    font-weight: 500;
    color: #1d1d1f;
    text-align: center;
}

/* ========== macOS 极简设备选择样式 ========== */

/* 设备项目容器 - 极简列表风格 */
.device-item {
    box-sizing: border-box;
    height: 32px;
    padding: 0 12px;
    border-radius: 4px;
    border: none;
    background: rgba(255, 255, 255, 0.85);
    transition: all 0.1s ease-out;
    cursor: pointer;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
}

/* 设备项目悬停效果 */
.device-item:hover {
    background: rgba(245, 245, 247, 0.9);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* 设备项目激活效果 */
.device-item:active {
    transform: scale(0.98);
    transition-duration: 0.05s;
}

/* 无权限设备项目样式 */
.device-item.disabled {
    background: rgba(248, 248, 248, 0.6);
    cursor: not-allowed;
    opacity: 0.4;
}

.device-item.disabled:hover {
    transform: none;
    background: rgba(248, 248, 248, 0.6);
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
}

/* 设备主信息容器 */
.device-main-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

/* 设备名称 */
.device-name-simple {
    font-size: 9px;
    font-weight: 500;
    color: #000;
    line-height: 1.2;
    white-space: nowrap;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 无权限设备名称颜色 */
.device-item.disabled .device-name-simple {
    color: #8e8e93;
}

/* 环境信息容器 */
.device-secondary-info {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

/* 环境标签 - 无背景极简版 */
.device-env-tag {
    padding: 0;
    border-radius: 0;
    font-size: 9px;
    font-weight: 500;
    line-height: 1;
    background: none;
    color: #007aff;
}

.device-env-tag.no-permission {
    color: #8e8e93;
}

/* 设备项目容器布局调整 - 与环境按钮保持一致的边距 */
.special-button-container .device-item {
    width: 100%;
    margin: 0;
}